"""
ECG Data Loader for Real Patient Recordings

This module loads actual ECG recordings from the database or file storage,
replacing the synthetic ECGSimulator for production use.
"""

import logging
import gzip
from typing import Optional, Dict, Any
from dataclasses import dataclass
from datetime import datetime

import numpy as np

logger = logging.getLogger(__name__)


@dataclass
class ECGRecording:
    """Represents a real ECG recording."""
    id: str
    patient_id: str
    signal: np.ndarray
    sample_rate: int
    recorded_at: datetime
    duration_seconds: float
    lead_type: str
    patient_name: str
    metadata: Dict[str, Any]
    classifications: List[Dict[str, Any]] = None  # Pre-loaded segment classifications
    annotations: List[Dict[str, Any]] = None  # Beat-level annotations (optional)


class ECGDataLoader:
    """
    Loads real ECG data from database or storage.

    This replaces ECGSimulator for production use with actual patient recordings.
    """

    def __init__(self, db_manager, sample_rate: int = 360):
        """
        Initialize the ECG data loader.

        Args:
            db_manager: Database manager for fetching recordings
            sample_rate: Expected sample rate (for validation)
        """
        self.db_manager = db_manager
        self.sample_rate = sample_rate

        logger.info(f"📁 ECGDataLoader initialized (sample rate: {sample_rate} Hz)")

    async def load_patient_recording(
        self,
        patient_id: str,
        recording_id: Optional[str] = None,
        load_classifications: bool = True,
        load_annotations: bool = False
    ) -> ECGRecording:
        """
        Load ECG recording for a patient.

        Args:
            patient_id: Patient identifier
            recording_id: Specific recording ID (None = most recent)
            load_classifications: Load pre-computed segment classifications
            load_annotations: Load beat-level annotations

        Returns:
            ECGRecording with signal data, metadata, and optional classifications
        """
        try:
            # Get patient info
            patient_info = await self.db_manager.get_patient_info(patient_id)
            if not patient_info:
                raise ValueError(f"Patient {patient_id} not found")

            patient_name = patient_info.get("name", "Unknown Patient")

            # Get recording ID if not provided
            if recording_id is None:
                recording_id = await self._get_latest_recording_id(patient_id)

            if recording_id is None:
                logger.warning(
                    f"⚠️  No recordings found for patient {patient_id}, "
                    f"using fallback data"
                )
                return self._create_fallback_recording(patient_id, patient_name)

            # Fetch recording from database
            recording_data = await self._fetch_recording(recording_id)

            # Decompress and validate signal
            signal = self._decompress_signal(recording_data['signal_data'])

            # Validate sample rate
            if recording_data['sample_rate'] != self.sample_rate:
                logger.warning(
                    f"⚠️  Recording sample rate ({recording_data['sample_rate']} Hz) "
                    f"differs from expected ({self.sample_rate} Hz), resampling..."
                )
                signal = self._resample_signal(
                    signal,
                    recording_data['sample_rate'],
                    self.sample_rate
                )

            # Load classifications if requested
            classifications = None
            if load_classifications:
                classifications = await self._load_classifications(recording_id)
                logger.info(f"📊 Loaded {len(classifications)} classifications for recording {recording_id}")

            # Load annotations if requested
            annotations = None
            if load_annotations:
                annotations = await self._load_annotations(recording_id)
                logger.info(f"📍 Loaded {len(annotations)} beat annotations for recording {recording_id}")

            return ECGRecording(
                id=recording_id,
                patient_id=patient_id,
                signal=signal,
                sample_rate=self.sample_rate,
                recorded_at=recording_data['recorded_at'],
                duration_seconds=len(signal) / self.sample_rate,
                lead_type=recording_data.get('lead_type', 'II'),
                patient_name=patient_name,
                metadata=recording_data.get('metadata', {}),
                classifications=classifications,
                annotations=annotations
            )

        except Exception as e:
            logger.error(f"❌ Error loading recording for patient {patient_id}: {e}")
            # Fallback to synthetic data for development
            return self._create_fallback_recording(patient_id, patient_name)

    async def _get_latest_recording_id(self, patient_id: str) -> Optional[str]:
        """
        Get the most recent recording ID for a patient.

        Args:
            patient_id: Patient identifier

        Returns:
            Recording ID or None if no recordings exist
        """
        try:
            query = """
                SELECT id
                FROM ecg_recordings
                WHERE patient_id = $1
                ORDER BY recorded_at DESC
                LIMIT 1
            """

            row = await self.db_manager._execute_fetchrow(query, patient_id)

            if row:
                return row['id']
            else:
                logger.warning(f"⚠️  No recordings found for patient {patient_id}")
                return None

        except Exception as e:
            logger.error(f"❌ Error fetching latest recording ID: {e}")
            return None

    async def _fetch_recording(self, recording_id: str) -> Dict[str, Any]:
        """
        Fetch recording data from database.

        Args:
            recording_id: Recording identifier

        Returns:
            Dictionary with recording data
        """
        try:
            query = """
                SELECT
                    id,
                    patient_id,
                    recorded_at,
                    duration_seconds,
                    sample_rate,
                    lead_type,
                    signal_data,
                    metadata
                FROM ecg_recordings
                WHERE id = $1
            """

            row = await self.db_manager._execute_fetchrow(query, recording_id)

            if not row:
                raise ValueError(f"Recording {recording_id} not found")

            return dict(row)

        except Exception as e:
            logger.error(f"❌ Error fetching recording {recording_id}: {e}")
            raise

    def _decompress_signal(self, compressed_data: str) -> np.ndarray:
        """
        Decompress ECG signal data.

        Args:
            compressed_data: Base64 encoded compressed signal data (string)

        Returns:
            Decompressed numpy array
        """
        try:
            import base64

            # Decode base64 string to bytes
            compressed_bytes = base64.b64decode(compressed_data)

            # Decompress
            decompressed = gzip.decompress(compressed_bytes)

            # Convert to numpy array (assuming float32)
            signal = np.frombuffer(decompressed, dtype=np.float32)

            logger.debug(f"📊 Decompressed signal: {len(signal)} samples")
            return signal

        except Exception as e:
            logger.error(f"❌ Error decompressing signal: {e}")
            raise

    def _resample_signal(
        self,
        signal: np.ndarray,
        original_rate: int,
        target_rate: int
    ) -> np.ndarray:
        """
        Resample signal to target sample rate.

        Args:
            signal: Original signal
            original_rate: Original sample rate
            target_rate: Target sample rate

        Returns:
            Resampled signal
        """
        try:
            from scipy import signal as scipy_signal

            # Calculate resampling ratio
            num_samples = int(len(signal) * target_rate / original_rate)

            # Resample
            resampled = scipy_signal.resample(signal, num_samples)

            logger.info(
                f"🔄 Resampled signal from {original_rate} Hz to {target_rate} Hz "
                f"({len(signal)} → {len(resampled)} samples)"
            )

            return resampled.astype(np.float32)

        except ImportError:
            logger.warning("⚠️  scipy not available, using simple interpolation")
            # Simple linear interpolation fallback
            return np.interp(
                np.linspace(0, len(signal), int(len(signal) * target_rate / original_rate)),
                np.arange(len(signal)),
                signal
            ).astype(np.float32)

    def _create_fallback_recording(
        self,
        patient_id: str,
        patient_name: str
    ) -> ECGRecording:
        """
        Create fallback synthetic recording for development.

        This is used when no real recordings are available.

        Args:
            patient_id: Patient identifier
            patient_name: Patient name

        Returns:
            Synthetic ECGRecording
        """
        logger.warning(
            f"⚠️  Creating fallback synthetic recording for patient {patient_id}"
        )

        # Generate simple synthetic ECG (10 seconds)
        duration = 10.0
        num_samples = int(duration * self.sample_rate)
        t = np.linspace(0, duration, num_samples)

        # Simple sine wave as placeholder
        signal = (
            np.sin(2 * np.pi * 1.2 * t) +
            0.1 * np.random.randn(len(t))
        ).astype(np.float32)

        return ECGRecording(
            id=f"fallback-{patient_id}",
            patient_id=patient_id,
            signal=signal,
            sample_rate=self.sample_rate,
            recorded_at=datetime.now(),
            duration_seconds=duration,
            lead_type="II",
            patient_name=patient_name,
            metadata={"source": "fallback", "synthetic": True}
        )


    async def _load_classifications(self, recording_id: str) -> List[Dict[str, Any]]:
        """
        Load pre-computed segment classifications for a recording.

        Args:
            recording_id: Recording identifier

        Returns:
            List of classification dictionaries
        """
        try:
            query = """
                SELECT
                    segment_start_time,
                    segment_duration,
                    classification,
                    confidence,
                    is_abnormal,
                    annotation_source,
                    metadata
                FROM ecg_segment_classifications
                WHERE recording_id = $1
                ORDER BY segment_start_time ASC
            """

            rows = await self.db_manager._execute_fetch(query, recording_id)

            return [
                {
                    'segment_start_time': float(row['segment_start_time']),
                    'segment_duration': float(row['segment_duration']),
                    'classification': row['classification'],
                    'confidence': float(row['confidence']),
                    'is_abnormal': row['is_abnormal'],
                    'annotation_source': row.get('annotation_source', 'unknown'),
                    'metadata': row.get('metadata', {})
                }
                for row in rows
            ]

        except Exception as e:
            logger.error(f"❌ Failed to load classifications for recording {recording_id}: {e}")
            return []

    async def _load_annotations(self, recording_id: str) -> List[Dict[str, Any]]:
        """
        Load beat-level annotations for a recording.

        Args:
            recording_id: Recording identifier

        Returns:
            List of annotation dictionaries
        """
        try:
            query = """
                SELECT
                    sample_number,
                    time_seconds,
                    annotation_type,
                    annotation_label,
                    confidence,
                    is_abnormal
                FROM ecg_annotations
                WHERE recording_id = $1
                ORDER BY sample_number ASC
            """

            rows = await self.db_manager._execute_fetch(query, recording_id)

            return [
                {
                    'sample_number': row['sample_number'],
                    'time_seconds': row['time_seconds'],
                    'annotation_type': row['annotation_type'],
                    'annotation_label': row['annotation_label'],
                    'confidence': row['confidence'],
                    'is_abnormal': row['is_abnormal']
                }
                for row in rows
            ]

        except Exception as e:
            logger.error(f"❌ Failed to load annotations for recording {recording_id}: {e}")
            return []

    async def get_recording_info(self, patient_id: str) -> Optional[Dict[str, Any]]:
        """
        Get information about patient's recordings.

        Args:
            patient_id: Patient identifier

        Returns:
            Dictionary with recording statistics
        """
        try:
            query = """
                SELECT
                    COUNT(*) as total_recordings,
                    MAX(recorded_at) as latest_recording,
                    SUM(duration_seconds) as total_duration_seconds
                FROM ecg_recordings
                WHERE patient_id = $1
            """

            row = await self.db_manager._execute_fetchrow(query, patient_id)

            if row:
                return {
                    "patient_id": patient_id,
                    "total_recordings": row['total_recordings'],
                    "latest_recording": row['latest_recording'],
                    "total_duration_seconds": row['total_duration_seconds'],
                    "has_recordings": row['total_recordings'] > 0
                }
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Error fetching recording info: {e}")
            return None

