"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import LightningECGChart from "@/components/LightningECGChart";
import ECGChart from "@/components/ECGChart";

/**
 * Test page to compare Canvas-based ECG chart vs LightningChart ECG chart
 * This allows us to evaluate performance and visual quality differences
 */
export default function TestLightningECGPage() {
  const [selectedPatient, setSelectedPatient] = useState("test-patient-1");
  const [showComparison, setShowComparison] = useState(false);

  const patients = [
    { id: "test-patient-1", name: "Test Patient (VTach)", condition: "Ventricular Tachycardia" },
    { id: "QqGq_lQX8-IMQqSjE_fPV", name: "<PERSON>", condition: "Normal Sinus Rhythm" },
    { id: "patient-2", name: "Patient 2", condition: "Atrial Fibrillation" },
    { id: "patient-3", name: "Patient 3", condition: "Bradycardia" },
  ];

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold">LightningChart ECG Performance Test</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Compare the performance and visual quality between our Canvas-based ECG chart 
          and the new LightningChart implementation. LightningChart is optimized for 
          high-frequency real-time data streaming.
        </p>
      </div>

      {/* Patient Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Patient Selection</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {patients.map((patient) => (
              <Button
                key={patient.id}
                variant={selectedPatient === patient.id ? "default" : "outline"}
                onClick={() => setSelectedPatient(patient.id)}
                className="h-auto p-4 flex flex-col items-start"
              >
                <span className="font-medium">{patient.name}</span>
                <span className="text-xs text-gray-500">{patient.condition}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Chart Comparison Toggle */}
      <div className="flex justify-center">
        <Button
          variant={showComparison ? "default" : "outline"}
          onClick={() => setShowComparison(!showComparison)}
        >
          {showComparison ? "Hide Comparison" : "Show Side-by-Side Comparison"}
        </Button>
      </div>

      {/* Performance Indicators */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Comparison</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <h3 className="font-semibold text-green-600">✅ LightningChart Advantages</h3>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• Optimized for 360Hz+ real-time data streaming</li>
                <li>• Hardware-accelerated WebGL rendering</li>
                <li>• Built-in progressive data patterns</li>
                <li>• Automatic buffer management (100k+ points)</li>
                <li>• Professional medical-grade visualization</li>
                <li>• Smooth scrolling with precise timing</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-orange-600">⚠️ Canvas Chart Limitations</h3>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• CPU-based rendering (performance bottleneck)</li>
                <li>• Manual timing and buffer management</li>
                <li>• Potential frame drops at high frequencies</li>
                <li>• Custom animation loop overhead</li>
                <li>• Limited to 30fps throttled rendering</li>
                <li>• Timing drift issues over time</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Charts */}
      <div className="space-y-6">
        {showComparison ? (
          // Side-by-side comparison
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Badge variant="default">NEW</Badge>
                <h2 className="text-xl font-semibold">LightningChart ECG</h2>
              </div>
              <LightningECGChart
                patientId={selectedPatient}
                height={400}
                showControls={true}
              />
            </div>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Badge variant="outline">LEGACY</Badge>
                <h2 className="text-xl font-semibold">Canvas ECG Chart</h2>
              </div>
              <ECGChart
                patientId={selectedPatient}
                height={400}
                showControls={true}
              />
            </div>
          </div>
        ) : (
          // Single chart view
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Badge variant="default">HIGH PERFORMANCE</Badge>
              <h2 className="text-xl font-semibold">LightningChart ECG Monitor</h2>
            </div>
            <LightningECGChart
              patientId={selectedPatient}
              height={500}
              showControls={true}
            />
          </div>
        )}
      </div>

      {/* Technical Notes */}
      <Card>
        <CardHeader>
          <CardTitle>Technical Implementation Notes</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">LightningChart Features Used:</h3>
            <ul className="text-sm space-y-1 text-gray-600 ml-4">
              <li>• <code>ProgressiveX</code> data pattern for time-series data</li>
              <li>• <code>AxisScrollStrategies.scrolling</code> for real-time scrolling</li>
              <li>• <code>setMaxSampleCount(100_000)</code> for efficient buffer management</li>
              <li>• <code>appendSamples()</code> for batch data streaming</li>
              <li>• WebGL-accelerated rendering for smooth performance</li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold mb-2">Data Flow:</h3>
            <p className="text-sm text-gray-600">
              ECG Server (360Hz) → WebSocket → React Hook → LightningChart → WebGL Rendering
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
