"""
WebSocket Handler for ECG Streaming

This module manages WebSocket connections for real-time ECG data streaming
and handles session management, authentication, and data broadcasting.
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, Set, Optional
from fastapi import WebSocket, WebSocketDisconnect

from .simulator import ECGSimulator
from .classifier import ECGClassifier
from ..database import DatabaseManager

logger = logging.getLogger(__name__)


class ECGWebSocketManager:
    """
    Manages WebSocket connections for ECG streaming.
    
    Handles multiple concurrent connections, session management,
    and real-time data broadcasting to authenticated clients.
    """
    
    def __init__(self):
        """Initialize the WebSocket manager."""
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        self.db_manager = DatabaseManager()
        self.ecg_simulator = ECGSimulator(db_manager=self.db_manager)
        self.classifier = ECGClassifier()
        self.classification_buffer: Dict[str, list] = {}

        logger.info("🔌 ECGWebSocketManager initialized")
    
    async def handle_connection(self, websocket: WebSocket, patient_id: str, doctor_id: str):
        """
        Handle a new WebSocket connection for ECG streaming.
        
        Args:
            websocket: WebSocket connection
            patient_id: Patient identifier
            doctor_id: Doctor identifier
        """
        session_id = None
        
        try:
            # Add connection to active connections
            if patient_id not in self.active_connections:
                self.active_connections[patient_id] = set()
            self.active_connections[patient_id].add(websocket)
            
            logger.info(f"🔗 New ECG connection: Doctor {doctor_id} -> Patient {patient_id}")
            
            # Create ECG session
            session_id = await self._create_ecg_session(patient_id, doctor_id)

            # Initialize classification buffer
            self.classification_buffer[session_id] = []

            # Add small delay to ensure WebSocket is fully ready
            await asyncio.sleep(0.1)

            # Send session start message
            await self._send_message(websocket, {
                "type": "session_start",
                "timestamp": datetime.now().isoformat(),
                "patient_id": patient_id,
                "data": {"session_id": session_id}
            })

            # Start ECG streaming
            await self._stream_ecg_data(websocket, patient_id, session_id)
            
        except WebSocketDisconnect:
            logger.info(f"🔌 WebSocket disconnected: Patient {patient_id}")
        except Exception as e:
            logger.error(f"❌ Error in ECG connection for patient {patient_id}: {e}")
            await self._send_error_message(websocket, patient_id, str(e))
        finally:
            # Cleanup
            await self._cleanup_connection(websocket, patient_id, session_id)
    
    async def verify_patient_access(self, doctor_user_id: str, patient_id: str) -> bool:
        """
        Verify that a doctor has access to a patient's ECG data.
        
        Args:
            doctor_user_id: Doctor's user ID
            patient_id: Patient ID
            
        Returns:
            True if access is granted, False otherwise
        """
        try:
            return await self.db_manager.verify_patient_access(doctor_user_id, patient_id)
        except Exception as e:
            logger.error(f"❌ Error verifying patient access: {e}")
            return False
    
    async def _stream_ecg_data(self, websocket: WebSocket, patient_id: str, session_id: str):
        """
        Stream ECG data and handle real-time classification.
        
        Args:
            websocket: WebSocket connection
            patient_id: Patient identifier
            session_id: ECG session identifier
        """
        try:
            async for sample in self.ecg_simulator.stream_patient_ecg(patient_id):
                # Send signal data
                success = await self._send_message(websocket, {
                    "type": "signal",
                    "timestamp": datetime.now().isoformat(),
                    "patient_id": patient_id,
                    "data": {"value": sample}
                })

                # If message sending fails, break the loop
                if not success:
                    logger.warning(f"⚠️  Message sending failed, stopping ECG stream for patient {patient_id}")
                    break

                # Add to classification buffer
                self.classification_buffer[session_id].append(sample)

                # Perform classification every 10 seconds (3600 samples at 360Hz)
                if len(self.classification_buffer[session_id]) >= 3600:
                    await self._perform_classification(
                        websocket, session_id, patient_id,
                        self.classification_buffer[session_id][-3600:]
                    )
                    
        except WebSocketDisconnect:
            raise
        except Exception as e:
            logger.error(f"❌ ECG streaming error for patient {patient_id}: {e}")
            raise
    
    async def _perform_classification(self, websocket: WebSocket, session_id: str, 
                                    patient_id: str, signal_segment: list):
        """
        Classify ECG segment and send results.
        
        Args:
            websocket: WebSocket connection
            session_id: ECG session identifier
            patient_id: Patient identifier
            signal_segment: ECG signal segment for classification
        """
        try:
            # Classify the ECG segment
            result = await self.classifier.classify_segment(signal_segment)
            
            # Store classification in database
            await self._store_classification(session_id, result)
            
            # Send classification result
            await self._send_message(websocket, {
                "type": "classification",
                "timestamp": datetime.now().isoformat(),
                "patient_id": patient_id,
                "data": {
                    "classification": {
                        "class": result["class"],
                        "confidence": result["confidence"],
                        "is_abnormal": result["is_abnormal"]
                    }
                }
            })
            
            # Send alert if abnormal
            if result["is_abnormal"]:
                await self._send_abnormality_alert(websocket, session_id, result)
                
        except Exception as e:
            logger.error(f"❌ Classification error for session {session_id}: {e}")
    
    async def _send_message(self, websocket: WebSocket, message: dict):
        """
        Send a message through WebSocket with improved error handling.

        Args:
            websocket: WebSocket connection
            message: Message dictionary to send
        """
        try:
            # Check if WebSocket is still connected
            if websocket.client_state.name != "CONNECTED":
                logger.warning(f"⚠️  WebSocket not connected (state: {websocket.client_state.name})")
                return False

            await websocket.send_text(json.dumps(message))
            return True
        except Exception as e:
            logger.error(f"❌ Failed to send WebSocket message: {e}")
            return False
    
    async def _send_error_message(self, websocket: WebSocket, patient_id: str, error_message: str):
        """
        Send an error message through WebSocket.
        
        Args:
            websocket: WebSocket connection
            patient_id: Patient identifier
            error_message: Error message to send
        """
        try:
            await self._send_message(websocket, {
                "type": "error",
                "timestamp": datetime.now().isoformat(),
                "patient_id": patient_id,
                "data": {"error_message": error_message}
            })
        except:
            pass  # Connection might be closed
    
    async def _send_abnormality_alert(self, websocket: WebSocket, session_id: str, result: dict):
        """
        Send abnormality alert.
        
        Args:
            websocket: WebSocket connection
            session_id: ECG session identifier
            result: Classification result
        """
        try:
            await self._send_message(websocket, {
                "type": "alert",
                "timestamp": datetime.now().isoformat(),
                "data": {
                    "session_id": session_id,
                    "alert_type": "abnormal_ecg",
                    "classification": result["class"],
                    "confidence": result["confidence"],
                    "message": f"Abnormal ECG detected: {result['class']}"
                }
            })
            
            # logger.warning(f"🚨 Abnormal ECG alert sent for session {session_id}: {result['class']}")
            
        except Exception as e:
            logger.error(f"❌ Failed to send abnormality alert: {e}")
    
    async def _create_ecg_session(self, patient_id: str, doctor_id: str) -> str:
        """
        Create a new ECG session in the database.
        
        Args:
            patient_id: Patient identifier
            doctor_id: Doctor identifier
            
        Returns:
            Session ID
        """
        try:
            session_id = f"ecg_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{patient_id[:8]}"
            
            # In a real implementation, this would create a database record
            logger.info(f"📝 Created ECG session: {session_id}")
            
            return session_id
            
        except Exception as e:
            logger.error(f"❌ Failed to create ECG session: {e}")
            raise
    
    async def _store_classification(self, session_id: str, result: dict):
        """
        Store classification result in database.
        
        Args:
            session_id: ECG session identifier
            result: Classification result
        """
        try:
            # In a real implementation, this would store in database
            logger.debug(f"💾 Stored classification for session {session_id}: {result['class']}")
            
        except Exception as e:
            logger.error(f"❌ Failed to store classification: {e}")
    
    async def _cleanup_connection(self, websocket: WebSocket, patient_id: str, session_id: Optional[str]):
        """
        Clean up connection and session data.
        
        Args:
            websocket: WebSocket connection
            patient_id: Patient identifier
            session_id: ECG session identifier (if created)
        """
        try:
            # Remove from active connections
            if patient_id in self.active_connections:
                self.active_connections[patient_id].discard(websocket)
                if not self.active_connections[patient_id]:
                    del self.active_connections[patient_id]
            
            # Clean up classification buffer
            if session_id and session_id in self.classification_buffer:
                del self.classification_buffer[session_id]
            
            # End ECG session
            if session_id:
                await self._end_ecg_session(session_id)
            
            logger.info(f"🧹 Cleaned up ECG connection for patient {patient_id}")
            
        except Exception as e:
            logger.error(f"❌ Error during connection cleanup: {e}")
    
    async def _end_ecg_session(self, session_id: str):
        """
        End an ECG session.
        
        Args:
            session_id: ECG session identifier
        """
        try:
            # In a real implementation, this would update the database
            logger.info(f"🏁 Ended ECG session: {session_id}")
            
        except Exception as e:
            logger.error(f"❌ Failed to end ECG session {session_id}: {e}")
    
    def get_active_connections_count(self) -> int:
        """
        Get the number of active connections.
        
        Returns:
            Number of active connections
        """
        return sum(len(connections) for connections in self.active_connections.values())
