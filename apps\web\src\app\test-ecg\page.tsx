"use client";

import { useState, useEffect } from "react";
import { authClient } from "@/lib/auth-client";
import EC<PERSON>hart from "@/components/ECGChart";
import LightningECGChart from "@/components/LightningECGChart";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

/**
 * ECG Streaming Test Page
 *
 * This page is used to test the complete ECG streaming workflow:
 * - Authentication validation
 * - WebSocket connection
 * - Real-time data streaming
 * - Error handling
 * - Component rendering
 */
export default function ECGTestPage() {
  const [testResults, setTestResults] = useState<Record<string, any>>({});
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [selectedTest, setSelectedTest] = useState<string | null>(null);
  const [useLightningChart, setUseLightningChart] = useState(true);

  const { data: session } = authClient.useSession();

  /**
   * Test 1: Authentication Validation
   */
  const testAuthentication = async () => {
    const testName = "authentication";
    setTestResults((prev) => ({ ...prev, [testName]: { status: "running" } }));

    try {
      const serverUrl =
        process.env.NEXT_PUBLIC_SERVER_URL || "http://localhost:3000";
      const response = await fetch(`${serverUrl}/api/auth/validate-session`, {
        method: "POST",
        credentials: "include",
      });

      if (response.ok) {
        const data = await response.json();
        setTestResults((prev) => ({
          ...prev,
          [testName]: {
            status: "success",
            data: {
              user: data.user,
              sessionValid: true,
              timestamp: new Date().toISOString(),
            },
          },
        }));
      } else {
        setTestResults((prev) => ({
          ...prev,
          [testName]: {
            status: "failed",
            error: `HTTP ${response.status}: ${response.statusText}`,
            timestamp: new Date().toISOString(),
          },
        }));
      }
    } catch (error) {
      setTestResults((prev) => ({
        ...prev,
        [testName]: {
          status: "failed",
          error: error instanceof Error ? error.message : "Unknown error",
          timestamp: new Date().toISOString(),
        },
      }));
    }
  };

  /**
   * Test 2: ECG Server Connectivity
   */
  const testECGServerConnectivity = async () => {
    const testName = "ecg-server";
    setTestResults((prev) => ({ ...prev, [testName]: { status: "running" } }));

    try {
      const ecgServerUrl =
        process.env.NEXT_PUBLIC_ECG_SERVER_URL || "http://localhost:8000";
      const response = await fetch(`${ecgServerUrl}/health`);

      if (response.ok) {
        const data = await response.json();
        setTestResults((prev) => ({
          ...prev,
          [testName]: {
            status: "success",
            data: {
              serverStatus: data.status,
              version: data.version,
              environment: data.environment,
              timestamp: new Date().toISOString(),
            },
          },
        }));
      } else {
        setTestResults((prev) => ({
          ...prev,
          [testName]: {
            status: "failed",
            error: `HTTP ${response.status}: ${response.statusText}`,
            timestamp: new Date().toISOString(),
          },
        }));
      }
    } catch (error) {
      setTestResults((prev) => ({
        ...prev,
        [testName]: {
          status: "failed",
          error:
            error instanceof Error ? error.message : "ECG server unreachable",
          timestamp: new Date().toISOString(),
        },
      }));
    }
  };

  /**
   * Test 3: Patient Data Access
   */
  const testPatientDataAccess = async () => {
    const testName = "patient-data";
    setTestResults((prev) => ({ ...prev, [testName]: { status: "running" } }));

    try {
      const ecgServerUrl =
        process.env.NEXT_PUBLIC_ECG_SERVER_URL || "http://localhost:8000";
      const response = await fetch(`${ecgServerUrl}/patients`, {
        method: "GET",
        credentials: "include",
      });

      if (response.ok) {
        const data = await response.json();
        setTestResults((prev) => ({
          ...prev,
          [testName]: {
            status: "success",
            data: {
              patientCount: data.patients?.length || 0,
              doctor: data.doctor,
              timestamp: new Date().toISOString(),
            },
          },
        }));
      } else {
        setTestResults((prev) => ({
          ...prev,
          [testName]: {
            status: "failed",
            error: `HTTP ${response.status}: ${response.statusText}`,
            timestamp: new Date().toISOString(),
          },
        }));
      }
    } catch (error) {
      setTestResults((prev) => ({
        ...prev,
        [testName]: {
          status: "failed",
          error: error instanceof Error ? error.message : "Unknown error",
          timestamp: new Date().toISOString(),
        },
      }));
    }
  };

  /**
   * Test 4: WebSocket Connection
   */
  const testWebSocketConnection = async () => {
    const testName = "websocket";
    setTestResults((prev) => ({ ...prev, [testName]: { status: "running" } }));

    try {
      // Get session cookie for WebSocket authentication
      const sessionCookie = document.cookie
        .split(";")
        .find((c) => c.trim().startsWith("better-auth.session_token="))
        ?.split("=")[1];

      if (!sessionCookie) {
        throw new Error("No session cookie found");
      }

      const ecgServerUrl =
        process.env.NEXT_PUBLIC_ECG_SERVER_URL || "ws://localhost:8000";
      const wsUrl = `${ecgServerUrl}/ws/ecg/test-patient?token=${sessionCookie}`;

      const ws = new WebSocket(wsUrl);
      let messageCount = 0;
      let sessionStartReceived = false;

      const timeout = setTimeout(() => {
        ws.close();
        setTestResults((prev) => ({
          ...prev,
          [testName]: {
            status: "failed",
            error: "WebSocket test timeout (10 seconds)",
            timestamp: new Date().toISOString(),
          },
        }));
      }, 10000);

      ws.onopen = () => {
        console.log("🔗 Test WebSocket connected");
      };

      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          messageCount++;

          if (message.type === "session_start") {
            sessionStartReceived = true;
          }

          // Consider test successful after receiving some messages
          if (messageCount >= 5 && sessionStartReceived) {
            clearTimeout(timeout);
            ws.close();
            setTestResults((prev) => ({
              ...prev,
              [testName]: {
                status: "success",
                data: {
                  messagesReceived: messageCount,
                  sessionStartReceived,
                  lastMessageType: message.type,
                  timestamp: new Date().toISOString(),
                },
              },
            }));
          }
        } catch (err) {
          console.error("Error parsing WebSocket message:", err);
        }
      };

      ws.onerror = () => {
        clearTimeout(timeout);
        setTestResults((prev) => ({
          ...prev,
          [testName]: {
            status: "failed",
            error: "WebSocket connection error",
            timestamp: new Date().toISOString(),
          },
        }));
      };

      ws.onclose = (event) => {
        if (
          event.code !== 1000 &&
          testResults[testName]?.status !== "success"
        ) {
          clearTimeout(timeout);
          setTestResults((prev) => ({
            ...prev,
            [testName]: {
              status: "failed",
              error: `WebSocket closed unexpectedly: ${event.code} ${event.reason}`,
              timestamp: new Date().toISOString(),
            },
          }));
        }
      };
    } catch (error) {
      setTestResults((prev) => ({
        ...prev,
        [testName]: {
          status: "failed",
          error: error instanceof Error ? error.message : "Unknown error",
          timestamp: new Date().toISOString(),
        },
      }));
    }
  };

  /**
   * Run all tests sequentially
   */
  const runAllTests = async () => {
    setIsRunningTests(true);
    setTestResults({});

    await testAuthentication();
    await new Promise((resolve) => setTimeout(resolve, 1000));

    await testECGServerConnectivity();
    await new Promise((resolve) => setTimeout(resolve, 1000));

    await testPatientDataAccess();
    await new Promise((resolve) => setTimeout(resolve, 1000));

    await testWebSocketConnection();

    setIsRunningTests(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "success":
        return "text-green-600";
      case "failed":
        return "text-red-600";
      case "running":
        return "text-blue-600";
      default:
        return "text-gray-600";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return "✅";
      case "failed":
        return "❌";
      case "running":
        return "🔄";
      default:
        return "⏳";
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">ECG Streaming Test Suite</h1>
          <p className="text-gray-600 mt-1">
            Comprehensive testing of the ECG streaming implementation
          </p>
        </div>
        <Button
          onClick={runAllTests}
          disabled={isRunningTests}
          className="min-w-32"
        >
          {isRunningTests ? "Running Tests..." : "Run All Tests"}
        </Button>
      </div>

      {/* Session Info */}
      <Card>
        <CardHeader>
          <CardTitle>Current Session</CardTitle>
        </CardHeader>
        <CardContent>
          {session ? (
            <div className="space-y-2">
              <p>
                <strong>User:</strong> {session.user.name} ({session.user.email}
                )
              </p>
              <p>
                <strong>Role:</strong> {(session.user as any).role}
              </p>
              <Badge variant="default">Authenticated</Badge>
            </div>
          ) : (
            <div>
              <p className="text-red-600">No active session</p>
              <Badge variant="destructive">Not Authenticated</Badge>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Test Results */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {[
          {
            key: "authentication",
            title: "Authentication Validation",
            description: "Tests Better Auth session validation",
          },
          {
            key: "ecg-server",
            title: "ECG Server Connectivity",
            description: "Tests connection to ECG streaming server",
          },
          {
            key: "patient-data",
            title: "Patient Data Access",
            description: "Tests fetching assigned patient data",
          },
          {
            key: "websocket",
            title: "WebSocket Connection",
            description: "Tests real-time ECG data streaming",
          },
        ].map((test) => {
          const result = testResults[test.key];
          return (
            <Card
              key={test.key}
              className="cursor-pointer"
              onClick={() => setSelectedTest(test.key)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{test.title}</CardTitle>
                  <span className="text-2xl">
                    {getStatusIcon(result?.status || "pending")}
                  </span>
                </div>
                <p className="text-sm text-gray-600">{test.description}</p>
              </CardHeader>
              <CardContent>
                <div
                  className={`font-medium ${getStatusColor(
                    result?.status || "pending"
                  )}`}
                >
                  Status: {result?.status || "pending"}
                </div>
                {result?.error && (
                  <p className="text-red-600 text-sm mt-1">
                    Error: {result.error}
                  </p>
                )}
                {result?.data && (
                  <p className="text-green-600 text-sm mt-1">
                    ✓ Test completed successfully
                  </p>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Test ECG Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Live ECG Chart Test</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <p className="text-gray-600">
              This chart uses Plotly.js with WebGL acceleration for
              high-performance real-time ECG visualization:
            </p>
            <div className="flex items-center space-x-2">
              <Button
                variant={useLightningChart ? "default" : "outline"}
                size="sm"
                onClick={() => setUseLightningChart(true)}
              >
                LightningChart
              </Button>
              <Button
                variant={!useLightningChart ? "default" : "outline"}
                size="sm"
                onClick={() => setUseLightningChart(false)}
              >
                Plotly.js Chart
              </Button>
            </div>
          </div>
          {useLightningChart ? (
            <LightningECGChart patientId="test-patient-1" height={300} />
          ) : (
            <ECGChart patientId="test-patient-1" height={300} />
          )}
        </CardContent>
      </Card>

      {/* Detailed Test Results */}
      {selectedTest && testResults[selectedTest] && (
        <Card>
          <CardHeader>
            <CardTitle>Detailed Results: {selectedTest}</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded-lg overflow-auto text-sm">
              {JSON.stringify(testResults[selectedTest], null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
