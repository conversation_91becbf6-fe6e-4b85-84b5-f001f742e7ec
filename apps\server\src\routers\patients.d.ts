/**
 * Patient Router
 *
 * Handles patient-related operations with proper authentication and authorization.
 * Only doctors can access patient data for patients assigned to them.
 */
export declare const patientRouter: import("@trpc/server").TRPCBuiltRouter<{
    ctx: {
        session: {
            session: {
                id: string;
                userId: string;
                expiresAt: Date;
                createdAt: Date;
                updatedAt: Date;
                token: string;
                ipAddress?: string | null | undefined | undefined;
                userAgent?: string | null | undefined | undefined;
            };
            user: {
                id: string;
                email: string;
                emailVerified: boolean;
                name: string;
                createdAt: Date;
                updatedAt: Date;
                image?: string | null | undefined | undefined;
                role: string | null | undefined;
                accountStatus: string | null | undefined;
                firstLogin: boolean | null | undefined;
            };
        } | null;
    };
    meta: object;
    errorShape: import("@trpc/server").TRPCDefaultErrorShape;
    transformer: false;
}, import("@trpc/server").TRPCDecorateCreateRouterOptions<{
    /**
     * Get patients assigned to the authenticated doctor
     */
    getAssignedPatients: import("@trpc/server").TRPCQueryProcedure<{
        input: void;
        output: {
            patients: {
                id: string;
                medicalRecordNumber: string | null;
                dateOfBirth: Date | null;
                gender: "male" | "female" | "other" | null;
                emergencyContact: string | null;
                emergencyPhone: string | null;
                createdAt: Date;
                name: string;
                email: string;
            }[];
            count: number;
            doctorId: string;
        };
        meta: object;
    }>;
    /**
     * Get specific patient information (if doctor has access)
     */
    getPatientById: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            patientId: string;
        };
        output: {
            patient: {
                id: string;
                medicalRecordNumber: string | null;
                dateOfBirth: Date | null;
                gender: "male" | "female" | "other" | null;
                emergencyContact: string | null;
                emergencyPhone: string | null;
                assignedDoctorId: string | null;
                createdAt: Date;
                updatedAt: Date;
                name: string;
                email: string;
                userId: string;
            };
            accessGranted: boolean;
            doctorId: string;
        };
        meta: object;
    }>;
    /**
     * Verify if doctor has access to a specific patient
     */
    verifyPatientAccess: import("@trpc/server").TRPCQueryProcedure<{
        input: {
            patientId: string;
        };
        output: {
            hasAccess: boolean;
            reason: string;
            doctorId?: undefined;
        } | {
            hasAccess: boolean;
            reason: string;
            doctorId: string;
        };
        meta: object;
    }>;
}>>;
