# Using Dataset Annotations for ECG Classification

**Date**: October 8, 2025  
**Approach**: Use pre-labeled ground truth from ECG datasets (MIT-BIH, PhysioNet) instead of ML model inference

---

## Overview

Instead of running ML model inference in real-time, we'll use the **expert-annotated labels** that come with ECG datasets like MIT-BIH. This approach:

✅ Uses validated ground truth from medical experts  
✅ No ML model training/deployment needed  
✅ Faster and more reliable for demonstration/testing  
✅ Perfect for CDSS development and validation  

---

## Architecture Recommendations

### Option 1: Pre-load Annotations with ECG Data (RECOMMENDED)

**When**: During data seeding (one-time operation)

**Flow**:
```
MIT-BIH Dataset Files
    ↓
Load ECG signal (.dat) + Annotations (.atr)
    ↓
Parse annotations (beat types, rhythms, timestamps)
    ↓
Store in database:
  - ecg_recordings (signal data)
  - ecg_annotations (beat-level labels)
  - ecg_classifications (segment-level summaries)
    ↓
Streaming: Load pre-computed classifications from DB
```

**Pros**:
- ✅ One-time processing during seeding
- ✅ Fast streaming (no computation needed)
- ✅ Annotations stored with signal for audit trail
- ✅ Can query/filter by classification type

**Cons**:
- ❌ Requires more database storage
- ❌ Initial seeding takes longer

---

### Option 2: Load Annotations On-Demand

**When**: During streaming initialization

**Flow**:
```
Patient Stream Request
    ↓
Load ECG signal from database
    ↓
Load annotation file from disk
    ↓
Parse annotations into memory
    ↓
Stream signal + send classifications at appropriate times
```

**Pros**:
- ✅ Less database storage
- ✅ Faster seeding

**Cons**:
- ❌ Slower stream initialization
- ❌ Requires annotation files on disk
- ❌ More complex streaming logic

---

## Recommended Approach: Option 1 (Pre-load)

### Database Schema

```typescript
// apps/server/src/db/schema/ecg.ts

export const ecgAnnotations = pgTable("ecg_annotations", {
  id: text("id").primaryKey(),
  recordingId: text("recording_id").references(() => ecgRecordings.id),
  sampleNumber: integer("sample_number").notNull(), // Position in signal
  timeSeconds: real("time_seconds").notNull(), // Time from start
  annotationType: text("annotation_type").notNull(), // 'N', 'V', 'A', etc.
  annotationLabel: text("annotation_label").notNull(), // 'Normal', 'PVC', 'PAC', etc.
  confidence: real("confidence").default(1.0), // Always 1.0 for ground truth
  isAbnormal: boolean("is_abnormal").notNull(),
  metadata: jsonb("metadata"), // Additional info from dataset
  createdAt: timestamp("created_at").default(sql`now()`),
});

export const ecgClassifications = pgTable("ecg_classifications", {
  id: text("id").primaryKey(),
  recordingId: text("recording_id").references(() => ecgRecordings.id),
  segmentStartTime: real("segment_start_time").notNull(), // Seconds
  segmentDuration: real("segment_duration").notNull(), // Seconds (e.g., 10.0)
  classification: text("classification").notNull(), // 'normal', 'afib', 'vtach', etc.
  confidence: real("confidence").notNull().default(1.0), // 1.0 for ground truth
  isAbnormal: boolean("is_abnormal").notNull(),
  annotationSource: text("annotation_source").notNull(), // 'MIT-BIH', 'PhysioNet', etc.
  metadata: jsonb("metadata"),
  createdAt: timestamp("created_at").default(sql`now()`),
});
```

---

## Implementation Strategy

### Phase 1: Data Loading (Seeding)

**File**: `apps/ecg-server/scripts/load_mitbih_data.py`

```python
import wfdb
import gzip
import numpy as np
from typing import List, Dict
import uuid

async def load_mitbih_recording(record_name: str, patient_id: str):
    """
    Load MIT-BIH recording with annotations.
    
    Args:
        record_name: MIT-BIH record number (e.g., '100', '101')
        patient_id: Patient ID to assign this recording to
    """
    # Load ECG signal
    record = wfdb.rdrecord(f'datasets/mitdb/{record_name}')
    signal = record.p_signal[:, 0]  # First lead (MLII)
    
    # Load annotations
    annotation = wfdb.rdann(f'datasets/mitdb/{record_name}', 'atr')
    
    # Compress signal
    compressed_signal = gzip.compress(signal.astype(np.float32).tobytes())
    
    # Insert recording
    recording_id = str(uuid.uuid4())
    await db.execute("""
        INSERT INTO ecg_recordings (
            id, patient_id, recorded_at, duration_seconds,
            sample_rate, lead_type, signal_data, metadata
        ) VALUES ($1, $2, NOW(), $3, $4, $5, $6, $7)
    """, 
        recording_id,
        patient_id,
        len(signal) / record.fs,
        record.fs,
        'MLII',
        compressed_signal,
        {'source': 'MIT-BIH', 'record': record_name}
    )
    
    # Insert beat-level annotations
    await insert_beat_annotations(recording_id, annotation, record.fs)
    
    # Create segment-level classifications (10-second windows)
    await create_segment_classifications(recording_id, annotation, signal, record.fs)
    
    return recording_id


async def insert_beat_annotations(
    recording_id: str, 
    annotation: wfdb.Annotation, 
    sample_rate: int
):
    """Insert individual beat annotations."""
    
    # MIT-BIH annotation symbols
    annotation_map = {
        'N': ('Normal', False),
        'L': ('Left bundle branch block', True),
        'R': ('Right bundle branch block', True),
        'A': ('Atrial premature beat', True),
        'V': ('Premature ventricular contraction', True),
        'F': ('Fusion of ventricular and normal', True),
        '/': ('Paced beat', True),
        'f': ('Fusion of paced and normal', True),
        'Q': ('Unclassifiable beat', True),
    }
    
    for i, (sample, symbol) in enumerate(zip(annotation.sample, annotation.symbol)):
        label, is_abnormal = annotation_map.get(symbol, ('Unknown', True))
        
        await db.execute("""
            INSERT INTO ecg_annotations (
                id, recording_id, sample_number, time_seconds,
                annotation_type, annotation_label, confidence, is_abnormal
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        """,
            str(uuid.uuid4()),
            recording_id,
            int(sample),
            float(sample / sample_rate),
            symbol,
            label,
            1.0,  # Ground truth = 100% confidence
            is_abnormal
        )


async def create_segment_classifications(
    recording_id: str,
    annotation: wfdb.Annotation,
    signal: np.ndarray,
    sample_rate: int,
    segment_duration: float = 10.0
):
    """
    Create 10-second segment classifications based on annotations.
    
    Determines overall rhythm/classification for each segment.
    """
    total_duration = len(signal) / sample_rate
    num_segments = int(total_duration / segment_duration)
    
    for seg_idx in range(num_segments):
        start_time = seg_idx * segment_duration
        end_time = start_time + segment_duration
        start_sample = int(start_time * sample_rate)
        end_sample = int(end_time * sample_rate)
        
        # Find annotations in this segment
        segment_annotations = [
            (sample, symbol) 
            for sample, symbol in zip(annotation.sample, annotation.symbol)
            if start_sample <= sample < end_sample
        ]
        
        # Determine segment classification
        classification, is_abnormal = classify_segment(segment_annotations)
        
        await db.execute("""
            INSERT INTO ecg_classifications (
                id, recording_id, segment_start_time, segment_duration,
                classification, confidence, is_abnormal, annotation_source
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        """,
            str(uuid.uuid4()),
            recording_id,
            start_time,
            segment_duration,
            classification,
            1.0,  # Ground truth
            is_abnormal,
            'MIT-BIH'
        )


def classify_segment(annotations: List[tuple]) -> tuple[str, bool]:
    """
    Classify a segment based on beat annotations.
    
    Returns: (classification_label, is_abnormal)
    """
    if not annotations:
        return ('normal', False)
    
    symbols = [symbol for _, symbol in annotations]
    
    # Count abnormal beats
    abnormal_count = sum(1 for s in symbols if s != 'N')
    total_count = len(symbols)
    
    # Determine classification
    if abnormal_count == 0:
        return ('normal', False)
    elif 'V' in symbols and abnormal_count / total_count > 0.3:
        return ('ventricular_tachycardia', True)
    elif 'A' in symbols and abnormal_count / total_count > 0.3:
        return ('atrial_fibrillation', True)
    elif abnormal_count / total_count > 0.1:
        return ('frequent_ectopy', True)
    else:
        return ('occasional_ectopy', True)
```

---

## Phase 2: Streaming Integration

### Update ECGDataLoader

```python
# apps/ecg-server/src/ecg/data_loader.py

@dataclass
class ECGRecording:
    """Represents a real ECG recording with annotations."""
    id: str
    patient_id: str
    signal: np.ndarray
    sample_rate: int
    recorded_at: datetime
    duration_seconds: float
    lead_type: str
    patient_name: str
    metadata: Dict[str, Any]
    classifications: List[Dict[str, Any]]  # NEW: Pre-loaded classifications


class ECGDataLoader:
    async def load_patient_recording(
        self, 
        patient_id: str,
        recording_id: Optional[str] = None,
        load_classifications: bool = True  # NEW parameter
    ) -> ECGRecording:
        """Load ECG recording with optional classifications."""
        
        # ... existing code to load signal ...
        
        # Load pre-computed classifications
        classifications = []
        if load_classifications:
            classifications = await self._load_classifications(recording_id)
        
        return ECGRecording(
            # ... existing fields ...
            classifications=classifications
        )
    
    async def _load_classifications(self, recording_id: str) -> List[Dict[str, Any]]:
        """Load pre-computed classifications for a recording."""
        query = """
            SELECT 
                segment_start_time,
                segment_duration,
                classification,
                confidence,
                is_abnormal,
                annotation_source
            FROM ecg_classifications
            WHERE recording_id = $1
            ORDER BY segment_start_time ASC
        """
        
        rows = await self.db_manager._execute_fetch(query, recording_id)
        return [dict(row) for row in rows]
```

---

## Phase 3: WebSocket Streaming

### Update PatientProducer

```python
# apps/ecg-server/src/ecg/streams.py

class PatientProducer:
    def __init__(self, patient_id: str, ecg_source: ECGSource, config: StreamConfig):
        # ... existing code ...
        
        # NEW: Pre-loaded classifications
        self.classifications = ecg_source.classifications
        self.last_classification_time = 0.0
    
    async def _produce_loop(self) -> None:
        """Main producer loop with classification timing."""
        signal_length = len(self.ecg_source.signal)
        chunk_interval = self.config.chunk_duration
        current_time = 0.0  # Track current playback time
        
        while self.is_running:
            # ... existing chunk extraction code ...
            
            # Check if we should send a classification
            classification = self._get_classification_at_time(current_time)
            if classification:
                await self._broadcast_classification(classification)
            
            current_time += chunk_interval
            await asyncio.sleep(chunk_interval)
    
    def _get_classification_at_time(self, current_time: float) -> Optional[Dict]:
        """Get classification for current playback time."""
        for classification in self.classifications:
            start = classification['segment_start_time']
            duration = classification['segment_duration']
            
            # Send classification at the start of each segment
            if start <= current_time < start + 0.25:  # Within first chunk
                if current_time > self.last_classification_time + duration:
                    self.last_classification_time = current_time
                    return classification
        
        return None
    
    async def _broadcast_classification(self, classification: Dict) -> None:
        """Broadcast classification to all subscribers."""
        # Similar to _broadcast_chunk but for classifications
        # Subscribers will receive this separately from signal chunks
        pass
```

---

## Answers to Your Questions

### 1. How to integrate into current architecture?

**Answer**: Use **Option 1 (Pre-load)** - Load annotations during seeding and store in database. This fits perfectly with the existing `PatientStreamRegistry` architecture.

### 2. When to load classification labels?

**Answer**: **During data seeding** (one-time). Store in `ecg_classifications` table. Load into memory when initializing `PatientProducer`.

### 3. How to pass labels to frontend?

**Answer**: **Via WebSocket** using existing message format:
```json
{
  "type": "classification",
  "timestamp": "2025-10-08T12:34:56.789Z",
  "patient_id": "patient-123",
  "data": {
    "classification": {
      "class": "ventricular_tachycardia",
      "confidence": 1.0,
      "is_abnormal": true
    }
  }
}
```

### 4. Pre-load into memory or fetch on-demand?

**Answer**: **Pre-load into memory** with the ECG signal. Classifications are small (few KB) and needed frequently during streaming.

---

## Next Steps

1. **Week 1**: Implement database schema + seeding script
2. **Week 2**: Update `ECGDataLoader` to load classifications
3. **Week 3**: Update `PatientProducer` to broadcast classifications
4. **Week 4**: Test end-to-end with MIT-BIH data

See `docs/dataset-annotations-implementation.md` for detailed code examples.

