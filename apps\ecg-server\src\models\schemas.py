"""
Pydantic schemas for ECG server data models.

This module defines the data structures used throughout the ECG server
for WebSocket communication, configuration, and database interactions.
"""

from datetime import datetime
from typing import Optional, Dict, Any, Literal
from pydantic import BaseModel, Field


class ECGMessage(BaseModel):
    """WebSocket message structure for ECG communication."""
    
    type: Literal["signal", "classification", "session_start", "session_end", "error"] = Field(
        ..., description="Type of ECG message"
    )
    timestamp: str = Field(..., description="ISO timestamp of the message")
    patient_id: str = Field(..., description="Patient identifier")
    data: Optional[Dict[str, Any]] = Field(None, description="Message payload")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class ECGClassification(BaseModel):
    """ECG classification result."""
    
    class_name: str = Field(..., alias="class", description="Classification result")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence score (0-1)")
    is_abnormal: bool = Field(..., description="Whether the classification indicates abnormality")
    
    class Config:
        """Pydantic configuration."""
        populate_by_name = True


class ECGStreamConfig(BaseModel):
    """Configuration for ECG streaming."""
    
    sample_rate: int = Field(360, ge=50, le=1000, description="ECG sample rate in Hz")
    buffer_size: int = Field(3600, ge=100, description="Buffer size for classification")
    classification_interval: int = Field(10, ge=1, le=60, description="Classification interval in seconds")
    
    class Config:
        """Pydantic configuration."""
        validate_assignment = True


class PatientInfo(BaseModel):
    """Patient information model."""
    
    id: str = Field(..., description="Patient unique identifier")
    medical_record_number: Optional[str] = Field(None, description="Medical record number")
    name: str = Field(..., description="Patient name")
    email: str = Field(..., description="Patient email")
    date_of_birth: Optional[datetime] = Field(None, description="Patient date of birth")
    gender: Optional[str] = Field(None, description="Patient gender")
    emergency_contact: Optional[str] = Field(None, description="Emergency contact name")
    emergency_phone: Optional[str] = Field(None, description="Emergency contact phone")
    assigned_doctor_id: Optional[str] = Field(None, description="Assigned doctor ID")
    doctor_name: Optional[str] = Field(None, description="Assigned doctor name")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class SessionInfo(BaseModel):
    """ECG session information model."""
    
    id: str = Field(..., description="Session unique identifier")
    patient_id: str = Field(..., description="Patient ID")
    doctor_id: str = Field(..., description="Doctor ID")
    start_time: datetime = Field(..., description="Session start time")
    end_time: Optional[datetime] = Field(None, description="Session end time")
    duration: Optional[int] = Field(None, description="Session duration in seconds")
    sample_rate: int = Field(360, description="ECG sample rate")
    status: Literal["active", "completed", "interrupted"] = Field(..., description="Session status")
    notes: Optional[str] = Field(None, description="Session notes")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class HealthResponse(BaseModel):
    """Health check response model."""
    
    status: Literal["healthy", "unhealthy"] = Field(..., description="Service status")
    service: str = Field(..., description="Service name")
    version: str = Field(..., description="Service version")
    environment: Dict[str, Any] = Field(..., description="Environment information")
    endpoints: Dict[str, str] = Field(..., description="Available endpoints")


class ErrorResponse(BaseModel):
    """Error response model."""
    
    error: str = Field(..., description="Error type")
    detail: str = Field(..., description="Error description")
    timestamp: str = Field(..., description="Error timestamp")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }