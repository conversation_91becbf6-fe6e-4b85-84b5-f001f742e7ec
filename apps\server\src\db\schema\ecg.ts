import {
  pgTable,
  text,
  timestamp,
  boolean,
  integer,
  decimal,
  real,
  jsonb,
  index,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import { user } from "./auth";
import { doctors } from "./doctors";

export const patients = pgTable(
  "patients",
  {
    id: text("id").primaryKey(),
    userId: text("user_id")
      .notNull()
      .references(() => user.id, { onDelete: "cascade" }),
    medicalRecordNumber: text("medical_record_number").unique(),
    dateOfBirth: timestamp("date_of_birth"),
    gender: text("gender", { enum: ["male", "female", "other"] }),
    emergencyContact: text("emergency_contact"),
    emergencyPhone: text("emergency_phone"),
    assignedDoctorId: text("assigned_doctor_id").references(() => doctors.id, {
      onDelete: "set null",
    }),
    createdAt: timestamp("created_at")
      .notNull()
      .default(sql`now()`),
    updatedAt: timestamp("updated_at")
      .notNull()
      .default(sql`now()`),
  },
  (table) => ({
    patientUserIdx: index("idx_patient_user_id").on(table.userId),
    patientDoctorIdx: index("idx_patient_doctor_id").on(table.assignedDoctorId),
    patientMrnIdx: index("idx_patient_mrn").on(table.medicalRecordNumber),
  })
);

export const ecgSessions = pgTable(
  "ecg_sessions",
  {
    id: text("id").primaryKey(),
    patientId: text("patient_id")
      .notNull()
      .references(() => patients.id, { onDelete: "cascade" }),
    doctorId: text("doctor_id")
      .notNull()
      .references(() => doctors.id, { onDelete: "cascade" }),
    startTime: timestamp("start_time")
      .notNull()
      .default(sql`now()`),
    endTime: timestamp("end_time"),
    duration: integer("duration"), // in seconds
    sampleRate: integer("sample_rate").notNull().default(360),
    status: text("status", {
      enum: ["active", "completed", "interrupted"],
    }).default("active"),
    notes: text("notes"),
    createdAt: timestamp("created_at")
      .notNull()
      .default(sql`now()`),
    updatedAt: timestamp("updated_at")
      .notNull()
      .default(sql`now()`),
  },
  (table) => ({
    sessionPatientIdx: index("idx_session_patient_id").on(table.patientId),
    sessionDoctorIdx: index("idx_session_doctor_id").on(table.doctorId),
    sessionStatusIdx: index("idx_session_status").on(table.status),
    sessionStartTimeIdx: index("idx_session_start_time").on(table.startTime),
  })
);

export const ecgClassifications = pgTable(
  "ecg_classifications",
  {
    id: text("id").primaryKey(),
    sessionId: text("session_id")
      .notNull()
      .references(() => ecgSessions.id, { onDelete: "cascade" }),
    timestamp: timestamp("timestamp")
      .notNull()
      .default(sql`now()`),
    classification: text("classification").notNull(),
    confidence: decimal("confidence", { precision: 5, scale: 4 }).notNull(),
    isAbnormal: boolean("is_abnormal").notNull().default(false),
    alertSent: boolean("alert_sent").notNull().default(false),
    createdAt: timestamp("created_at")
      .notNull()
      .default(sql`now()`),
  },
  (table) => ({
    classificationSessionIdx: index("idx_classification_session_id").on(
      table.sessionId
    ),
    classificationTimestampIdx: index("idx_classification_timestamp").on(
      table.timestamp
    ),
    classificationAbnormalIdx: index("idx_classification_abnormal").on(
      table.isAbnormal
    ),
    classificationAlertIdx: index("idx_classification_alert").on(
      table.alertSent
    ),
  })
);

// New tables for real ECG data with dataset annotations

export const ecgRecordings = pgTable(
  "ecg_recordings",
  {
    id: text("id").primaryKey(),
    patientId: text("patient_id")
      .notNull()
      .references(() => patients.id, { onDelete: "cascade" }),
    recordedAt: timestamp("recorded_at")
      .notNull()
      .default(sql`now()`),
    durationSeconds: real("duration_seconds").notNull(),
    sampleRate: integer("sample_rate").notNull().default(360),
    leadType: text("lead_type").notNull().default("II"), // Lead type (II, MLII, V5, etc.)
    signalData: text("signal_data").notNull(), // Base64 encoded compressed signal
    metadata: jsonb("metadata"), // Additional metadata (source, condition, etc.)
    createdAt: timestamp("created_at")
      .notNull()
      .default(sql`now()`),
  },
  (table) => ({
    recordingPatientIdx: index("idx_recording_patient_id").on(table.patientId),
    recordingDateIdx: index("idx_recording_date").on(table.recordedAt),
  })
);

export const ecgAnnotations = pgTable(
  "ecg_annotations",
  {
    id: text("id").primaryKey(),
    recordingId: text("recording_id")
      .notNull()
      .references(() => ecgRecordings.id, { onDelete: "cascade" }),
    sampleNumber: integer("sample_number").notNull(), // Position in signal
    timeSeconds: real("time_seconds").notNull(), // Time from recording start
    annotationType: text("annotation_type").notNull(), // 'N', 'V', 'A', etc.
    annotationLabel: text("annotation_label").notNull(), // 'Normal', 'PVC', 'PAC', etc.
    confidence: real("confidence").notNull().default(1.0), // 1.0 for ground truth
    isAbnormal: boolean("is_abnormal").notNull(),
    createdAt: timestamp("created_at")
      .notNull()
      .default(sql`now()`),
  },
  (table) => ({
    annotationRecordingIdx: index("idx_annotation_recording_id").on(
      table.recordingId
    ),
    annotationTimeIdx: index("idx_annotation_time").on(table.timeSeconds),
  })
);

export const ecgSegmentClassifications = pgTable(
  "ecg_segment_classifications",
  {
    id: text("id").primaryKey(),
    recordingId: text("recording_id")
      .notNull()
      .references(() => ecgRecordings.id, { onDelete: "cascade" }),
    segmentStartTime: real("segment_start_time").notNull(), // Seconds from recording start
    segmentDuration: real("segment_duration").notNull().default(10.0), // Usually 10 seconds
    classification: text("classification").notNull(), // 'normal', 'afib', 'vtach', etc.
    confidence: real("confidence").notNull().default(1.0), // 1.0 for ground truth
    isAbnormal: boolean("is_abnormal").notNull(),
    annotationSource: text("annotation_source").notNull(), // 'MIT-BIH', 'PhysioNet', etc.
    metadata: jsonb("metadata"), // Additional metadata
    createdAt: timestamp("created_at")
      .notNull()
      .default(sql`now()`),
  },
  (table) => ({
    segmentRecordingIdx: index("idx_segment_recording_id").on(
      table.recordingId
    ),
    segmentTimeIdx: index("idx_segment_time").on(table.segmentStartTime),
    segmentClassificationIdx: index("idx_segment_classification").on(
      table.classification
    ),
  })
);