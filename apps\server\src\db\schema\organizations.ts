import { pgTable, text, timestamp, boolean, index } from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import { user } from "./auth";

export const organizations = pgTable("organizations", {
	id: text("id").primaryKey(),
	name: text("name").notNull(),
	adminEmail: text("admin_email").notNull(),
	address: text("address"),
	phone: text("phone"),
	website: text("website"),
	isActive: boolean("is_active").notNull().default(true),
	createdAt: timestamp("created_at").notNull().default(sql`now()`),
	updatedAt: timestamp("updated_at").notNull().default(sql`now()`),
}, (table) => ({
	orgIsActiveIdx: index("idx_org_is_active").on(table.isActive),
}));

export const organizationInvitations = pgTable("organization_invitations", {
	id: text("id").primary<PERSON>ey(),
	organizationId: text("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
	email: text("email").notNull(),
	invitedBy: text("invited_by").notNull().references(() => user.id, { onDelete: "cascade" }),
	role: text("role").notNull(),
	token: text("token").notNull().unique(),
	expiresAt: timestamp("expires_at").notNull(),
	acceptedAt: timestamp("accepted_at"),
	createdAt: timestamp("created_at").notNull().default(sql`now()`),
});

export const passwordResetTokens = pgTable("password_reset_tokens", {
	id: text("id").primaryKey(),
	userId: text("user_id").notNull().references(() => user.id, { onDelete: "cascade" }),
	token: text("token").notNull().unique(),
	expiresAt: timestamp("expires_at").notNull(),
	used: boolean("used").notNull().default(false),
	createdAt: timestamp("created_at").notNull().default(sql`now()`),
});