/**
 * LightningChart License Management Utility
 * 
 * Helps manage LightningChart JS license configuration and validation
 */

export interface LicenseInfo {
  isValid: boolean;
  type: 'trial' | 'deployment' | 'developer' | 'none';
  expiryDate?: Date;
  features: string[];
  watermark: boolean;
}

export interface LicenseValidationResult {
  success: boolean;
  error?: string;
  info?: LicenseInfo;
  recommendation?: string;
}

/**
 * Validate LightningChart license format and type
 */
export function validateLicenseFormat(license: string): LicenseValidationResult {
  if (!license || license.trim() === '') {
    return {
      success: false,
      error: 'No license provided',
      recommendation: 'Get trial license at https://lightningchart.com/js-charts/download/',
    };
  }

  // Check license format patterns
  const deploymentKeyPattern = /^0002-[A-Za-z0-9+/]+-[A-Za-z0-9+/]+-[A-Za-z0-9+/]+$/;
  const trialKeyPattern = /^[A-Za-z0-9]{8}-[A-Za-z0-9]{4}-[A-Za-z0-9]{4}-[A-Za-z0-9]{4}-[A-Za-z0-9]{12}$/;
  
  if (deploymentKeyPattern.test(license)) {
    return {
      success: false,
      error: 'Deployment key detected - not valid for trial use',
      recommendation: 'Get trial license at https://lightningchart.com/js-charts/download/',
      info: {
        isValid: false,
        type: 'deployment',
        features: ['Production validation only'],
        watermark: true,
      },
    };
  }

  if (trialKeyPattern.test(license)) {
    return {
      success: true,
      info: {
        isValid: true,
        type: 'trial',
        features: ['Full functionality', '30-day trial'],
        watermark: true,
      },
    };
  }

  // Check for other patterns
  if (license.startsWith('0001-') || license.startsWith('0003-')) {
    return {
      success: true,
      info: {
        isValid: true,
        type: 'developer',
        features: ['Full functionality', 'No watermark'],
        watermark: false,
      },
    };
  }

  return {
    success: false,
    error: 'Unknown license format',
    recommendation: 'Verify license format or get new trial license',
  };
}

/**
 * Get license configuration recommendations
 */
export function getLicenseRecommendations(): {
  trial: string;
  development: string;
  production: string;
} {
  return {
    trial: `
// For 30-day trial evaluation:
// 1. Visit: https://lightningchart.com/js-charts/download/
// 2. Fill out trial form
// 3. Check email for trial license
// 4. Replace YOUR_TRIAL_LICENSE_HERE with actual license

export const LIGHTNINGCHART_CONFIG = {
  license: "YOUR_TRIAL_LICENSE_HERE",
  licenseInformation: {
    appTitle: "HealthLink ECG Monitor",
    company: "HealthLink Medical Systems",
  },
};`,

    development: `
// For development without license (shows watermark):
// This mode works immediately but shows "Click here to get license"

export const LIGHTNINGCHART_CONFIG = {
  // No license property = trial mode with watermark
  licenseInformation: {
    appTitle: "HealthLink ECG Monitor", 
    company: "HealthLink Medical Systems",
  },
};`,

    production: `
// For production use:
// 1. Purchase LightningChart license
// 2. Use provided production license key
// 3. No watermark, full commercial use

export const LIGHTNINGCHART_CONFIG = {
  license: "YOUR_PRODUCTION_LICENSE_HERE",
  licenseInformation: {
    appTitle: "HealthLink ECG Monitor",
    company: "HealthLink Medical Systems", 
  },
};`,
  };
}

/**
 * Check if current environment should use license
 */
export function shouldUseLicense(): boolean {
  // In development, license is optional (will show watermark)
  // In production, license is required
  return process.env.NODE_ENV === 'production';
}

/**
 * Get license status message for UI
 */
export function getLicenseStatusMessage(
  hasLicense: boolean,
  error?: string
): {
  message: string;
  type: 'success' | 'warning' | 'error';
  action?: string;
} {
  if (error) {
    if (error.toLowerCase().includes('deployment key')) {
      return {
        message: 'Invalid license type - deployment key not valid for trial use',
        type: 'error',
        action: 'Get trial license at lightningchart.com/js-charts/download/',
      };
    }
    
    return {
      message: `License error: ${error}`,
      type: 'error',
      action: 'Check license configuration or get new trial license',
    };
  }

  if (!hasLicense) {
    return {
      message: 'Using trial mode with watermark',
      type: 'warning',
      action: 'Get trial license for watermark-free evaluation',
    };
  }

  return {
    message: 'Licensed version active',
    type: 'success',
  };
}

/**
 * Generate license configuration code
 */
export function generateLicenseConfig(
  licenseKey?: string,
  appTitle: string = 'HealthLink ECG Monitor',
  company: string = 'HealthLink Medical Systems'
): string {
  if (!licenseKey) {
    return `export const LIGHTNINGCHART_CONFIG = {
  // No license = trial mode with watermark
  licenseInformation: {
    appTitle: "${appTitle}",
    company: "${company}",
  },
} as const;`;
  }

  return `export const LIGHTNINGCHART_CONFIG = {
  license: "${licenseKey}",
  licenseInformation: {
    appTitle: "${appTitle}",
    company: "${company}",
  },
} as const;`;
}

/**
 * License troubleshooting guide
 */
export const LICENSE_TROUBLESHOOTING = {
  'deployment_key_invalid': {
    problem: 'Using deployment key instead of trial license',
    solution: 'Get trial license at https://lightningchart.com/js-charts/download/',
    explanation: 'Deployment keys are for production license validation, not trial use',
  },
  
  'license_expired': {
    problem: 'Trial license has expired',
    solution: 'Contact LightningChart for license renewal or purchase',
    explanation: 'Trial licenses are valid for 30 days from issue date',
  },
  
  'no_license': {
    problem: 'No license configured',
    solution: 'Either get trial license or use without license (shows watermark)',
    explanation: 'LightningChart works without license but shows watermark and "Click here to get license"',
  },
  
  'invalid_format': {
    problem: 'License format not recognized',
    solution: 'Verify license key format or get new license',
    explanation: 'License keys have specific formats for different license types',
  },
} as const;

/**
 * Quick setup instructions
 */
export const QUICK_SETUP_INSTRUCTIONS = `
🚀 LightningChart License Quick Setup

1. IMMEDIATE USE (with watermark):
   - Comment out license line in lightningchart-config.ts
   - Chart works immediately with "Click here to get license" watermark

2. TRIAL LICENSE (30 days, with small watermark):
   - Visit: https://lightningchart.com/js-charts/download/
   - Fill form with your details
   - Check email for trial license
   - Add license to lightningchart-config.ts

3. PRODUCTION LICENSE (no watermark):
   - Purchase license from LightningChart
   - Use provided production license key
   - Full commercial use rights

Current recommendation: Use option 1 for immediate testing, then get trial license for evaluation.
`;
