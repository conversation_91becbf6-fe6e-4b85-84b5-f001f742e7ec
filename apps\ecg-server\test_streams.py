"""
Test script for Milestone M1: Patient Stream Registry

This script tests the continuous producer and subscribe API without WebSocket.

Success Criteria:
1. Patient-to-ECG mapping is deterministic and reproducible
2. Producers generate chunks at correct timing (360 Hz, 0.25s chunks = 90 samples)
3. Multiple subscribers receive identical chunks
4. Chunk cadence is consistent (4 chunks/second)
"""

import asyncio
import logging
import time
import os
from typing import List
import numpy as np
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from src.database import DatabaseManager
from src.ecg.streams import PatientStreamRegistry, StreamConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_patient_mapping():
    """Test 1: Deterministic patient-to-ECG mapping."""
    logger.info("=" * 60)
    logger.info("TEST 1: Deterministic Patient-to-ECG Mapping")
    logger.info("=" * 60)
    
    db_manager = DatabaseManager()
    await db_manager.create_pool()
    
    # Create two registries with same seed
    registry1 = PatientStreamRegistry(db_manager, seed=42)
    registry2 = PatientStreamRegistry(db_manager, seed=42)
    
    # Initialize with same patients
    test_patients = ["test-patient-1"]
    
    count1 = await registry1.initialize_patients(test_patients)
    count2 = await registry2.initialize_patients(test_patients)
    
    logger.info(f"Registry 1 initialized: {count1} patients")
    logger.info(f"Registry 2 initialized: {count2} patients")
    
    # Verify mappings are identical
    for patient_id in test_patients:
        info1 = registry1.get_patient_info(patient_id)
        info2 = registry2.get_patient_info(patient_id)
        
        if info1 and info2:
            logger.info(f"\nPatient: {patient_id}")
            logger.info(f"  Registry 1: {info1['condition']}, signal_length={info1['signal_length']}")
            logger.info(f"  Registry 2: {info2['condition']}, signal_length={info2['signal_length']}")
            
            assert info1['condition'] == info2['condition'], "Conditions don't match!"
            assert info1['signal_length'] == info2['signal_length'], "Signal lengths don't match!"
            logger.info("  ✅ Mappings are identical (deterministic)")
        else:
            logger.error(f"  ❌ Patient {patient_id} not found in one or both registries")
    
    await db_manager.close_pool()
    logger.info("\n✅ TEST 1 PASSED: Deterministic mapping verified\n")


async def test_producer_timing():
    """Test 2: Producer chunk timing and size."""
    logger.info("=" * 60)
    logger.info("TEST 2: Producer Chunk Timing and Size")
    logger.info("=" * 60)
    
    db_manager = DatabaseManager()
    await db_manager.create_pool()
    
    config = StreamConfig(sample_rate=360, chunk_duration=0.25)
    registry = PatientStreamRegistry(db_manager, config=config, seed=42)
    
    # Initialize with test patient
    await registry.initialize_patients(["test-patient-1"])
    
    # Start producers
    await registry.start_all()
    
    # Subscribe to stream
    subscriber_id = "test-subscriber-1"
    stream = await registry.subscribe("test-patient-1", subscriber_id)
    
    if stream is None:
        logger.error("❌ Failed to subscribe to patient stream")
        await registry.stop_all()
        await db_manager.close_pool()
        return
    
    # Collect chunks and measure timing
    chunk_count = 0
    chunk_sizes = []
    chunk_times = []
    start_time = time.time()
    
    logger.info(f"Collecting chunks for 2 seconds...")
    
    async for chunk in stream:
        chunk_count += 1
        chunk_sizes.append(len(chunk))
        chunk_times.append(time.time() - start_time)
        
        if chunk_count >= 8:  # 2 seconds worth (4 chunks/sec * 2)
            break
    
    # Unsubscribe
    await registry.unsubscribe("test-patient-1", subscriber_id)
    
    # Stop producers
    await registry.stop_all()
    await db_manager.close_pool()
    
    # Analyze results
    logger.info(f"\n📊 Results:")
    logger.info(f"  Total chunks received: {chunk_count}")
    logger.info(f"  Expected chunk size: {config.chunk_size} samples")
    logger.info(f"  Actual chunk sizes: {chunk_sizes}")
    logger.info(f"  All chunks correct size: {all(s == config.chunk_size for s in chunk_sizes)}")
    
    # Calculate inter-chunk intervals
    if len(chunk_times) > 1:
        intervals = [chunk_times[i] - chunk_times[i-1] for i in range(1, len(chunk_times))]
        avg_interval = sum(intervals) / len(intervals)
        logger.info(f"  Expected interval: {config.chunk_duration}s")
        logger.info(f"  Average interval: {avg_interval:.3f}s")
        logger.info(f"  Interval variance: {np.std(intervals):.4f}s")
    
    # Verify
    assert all(s == config.chunk_size for s in chunk_sizes), "Chunk sizes incorrect!"
    assert chunk_count >= 7, f"Expected ~8 chunks, got {chunk_count}"
    
    logger.info("\n✅ TEST 2 PASSED: Timing and chunk size verified\n")


async def test_multiple_subscribers():
    """Test 3: Multiple subscribers receive identical chunks."""
    logger.info("=" * 60)
    logger.info("TEST 3: Multiple Subscribers Receive Identical Chunks")
    logger.info("=" * 60)
    
    db_manager = DatabaseManager()
    await db_manager.create_pool()
    
    registry = PatientStreamRegistry(db_manager, seed=42)
    await registry.initialize_patients(["test-patient-1"])
    await registry.start_all()
    
    # Create two subscribers
    stream1 = await registry.subscribe("test-patient-1", "subscriber-1")
    stream2 = await registry.subscribe("test-patient-1", "subscriber-2")
    
    if stream1 is None or stream2 is None:
        logger.error("❌ Failed to create subscribers")
        await registry.stop_all()
        await db_manager.close_pool()
        return
    
    # Collect chunks from both streams
    chunks1 = []
    chunks2 = []
    
    async def collect_chunks(stream, chunk_list, name):
        count = 0
        async for chunk in stream:
            chunk_list.append(chunk.copy())
            count += 1
            if count >= 5:
                break
        logger.info(f"  {name} collected {count} chunks")
    
    # Run both collectors concurrently
    await asyncio.gather(
        collect_chunks(stream1, chunks1, "Subscriber 1"),
        collect_chunks(stream2, chunks2, "Subscriber 2")
    )
    
    # Unsubscribe
    await registry.unsubscribe("test-patient-1", "subscriber-1")
    await registry.unsubscribe("test-patient-1", "subscriber-2")
    
    # Stop producers
    await registry.stop_all()
    await db_manager.close_pool()
    
    # Verify chunks are identical
    logger.info(f"\n📊 Results:")
    logger.info(f"  Subscriber 1 chunks: {len(chunks1)}")
    logger.info(f"  Subscriber 2 chunks: {len(chunks2)}")
    
    min_chunks = min(len(chunks1), len(chunks2))
    identical = True
    
    for i in range(min_chunks):
        if not np.array_equal(chunks1[i], chunks2[i]):
            identical = False
            logger.error(f"  ❌ Chunk {i} differs between subscribers")
            break
    
    if identical:
        logger.info(f"  ✅ All {min_chunks} chunks are identical across subscribers")
    
    assert identical, "Chunks are not identical across subscribers!"
    logger.info("\n✅ TEST 3 PASSED: Multiple subscribers verified\n")


async def test_registry_stats():
    """Test 4: Registry statistics."""
    logger.info("=" * 60)
    logger.info("TEST 4: Registry Statistics")
    logger.info("=" * 60)
    
    db_manager = DatabaseManager()
    await db_manager.create_pool()
    
    registry = PatientStreamRegistry(db_manager, seed=42)
    
    # Fetch all patients from database
    count = await registry.initialize_patients()
    logger.info(f"Initialized {count} patients from database")
    
    # Start all producers
    await registry.start_all()
    
    # Get stats
    stats = registry.get_stats()
    
    logger.info(f"\n📊 Registry Statistics:")
    logger.info(f"  Total patients: {stats['total_patients']}")
    logger.info(f"  Running producers: {stats['running_producers']}")
    logger.info(f"  Total subscribers: {stats['total_subscribers']}")
    logger.info(f"  Config: {stats['config']}")
    
    logger.info(f"\n  Patient Details:")
    for patient_id, producer_stats in stats['producers'].items():
        logger.info(f"    {producer_stats['patient_name']} ({patient_id}):")
        logger.info(f"      Condition: {producer_stats['condition']}")
        logger.info(f"      Running: {producer_stats['is_running']}")
        logger.info(f"      Chunks sent: {producer_stats['chunks_sent']}")
    
    # Wait a bit to accumulate some chunks
    await asyncio.sleep(1.0)
    
    # Get updated stats
    stats = registry.get_stats()
    logger.info(f"\n  After 1 second:")
    for patient_id, producer_stats in stats['producers'].items():
        logger.info(f"    {producer_stats['patient_name']}: {producer_stats['chunks_sent']} chunks")
    
    # Stop all
    await registry.stop_all()
    await db_manager.close_pool()
    
    logger.info("\n✅ TEST 4 PASSED: Statistics verified\n")


async def main():
    """Run all tests."""
    logger.info("\n" + "=" * 60)
    logger.info("MILESTONE M1 TEST SUITE")
    logger.info("Patient Stream Registry & Subscribe API")
    logger.info("=" * 60 + "\n")
    
    try:
        await test_patient_mapping()
        await test_producer_timing()
        await test_multiple_subscribers()
        await test_registry_stats()
        
        logger.info("\n" + "=" * 60)
        logger.info("🎉 ALL TESTS PASSED!")
        logger.info("=" * 60 + "\n")
        
    except Exception as e:
        logger.error(f"\n❌ TEST FAILED: {e}", exc_info=True)
        raise


if __name__ == "__main__":
    asyncio.run(main())

