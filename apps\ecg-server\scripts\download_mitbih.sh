#!/bin/bash
# Download MIT-BIH Arrhythmia Database sample records

set -e

# Create directory
mkdir -p datasets/mitdb
cd datasets/mitdb

echo "📥 Downloading MIT-BIH Arrhythmia Database sample records..."

# Base URL
BASE_URL="https://physionet.org/files/mitdb/1.0.0"

# Records to download (different arrhythmia types)
# 100: Normal sinus rhythm
# 106: Atrial fibrillation  
# 119: Ventricular tachycardia
# 200: Frequent PVCs
RECORDS=("100" "106" "119" "200")

for record in "${RECORDS[@]}"; do
    echo ""
    echo "📦 Downloading record $record..."
    
    # Download .dat file (signal data)
    if [ ! -f "${record}.dat" ]; then
        wget -q "${BASE_URL}/${record}.dat"
        echo "  ✅ ${record}.dat"
    else
        echo "  ⏭️  ${record}.dat (already exists)"
    fi
    
    # Download .hea file (header)
    if [ ! -f "${record}.hea" ]; then
        wget -q "${BASE_URL}/${record}.hea"
        echo "  ✅ ${record}.hea"
    else
        echo "  ⏭️  ${record}.hea (already exists)"
    fi
    
    # Download .atr file (annotations)
    if [ ! -f "${record}.atr" ]; then
        wget -q "${BASE_URL}/${record}.atr"
        echo "  ✅ ${record}.atr"
    else
        echo "  ⏭️  ${record}.atr (already exists)"
    fi
done

echo ""
echo "🎉 Download complete!"
echo ""
echo "Downloaded records:"
ls -lh *.dat | awk '{print "  " $9 " (" $5 ")"}'

