# ECG Issue 2: Overlapping Signals Analysis

## Issue Description
Multiple ECG signals are overlapping with slight time shifts, creating a distorted display where it looks like multiple waveforms are layered on top of each other.

## Root Cause Analysis

### 🔍 **Primary Causes Identified:**

#### 1. **Missing Patient Switch State Reset**
**Location**: `SimpleECGChart.tsx` - **CRITICAL ISSUE**

**Problem**: Chart component has NO state reset logic when `patientId` prop changes:
- `chartData` state persists between patient switches
- Chart labels and data arrays accumulate values
- No useEffect dependency on `patientId`

```typescript
// ❌ MISSING in SimpleECGChart.tsx
// No useEffect for patientId changes
// No chart data clearing when patient switches
// No state reset mechanism
```

**Evidence**: Code analysis revealed:
```typescript
// SimpleECGChart.tsx:58-73
const [chartData, setChartData] = useState<ChartData<"line">>({
  labels: [],
  datasets: [{
    label: "ECG Signal",
    data: [], // ❌ This never gets reset when patientId changes
    // ...
  }],
});
```

#### 2. **Shared Signal Buffer in useECGStream**
**Location**: `useECGStream.ts:140-146`

**Problem**: The signal buffer is shared between chart instances and not cleared on patient switch:

```typescript
setSignal(prev => {
  const newSignal = [...prev, dataPoint]; // ❌ Accumulates all patients' data
  return newSignal.length > maxSignalBuffer
    ? newSignal.slice(-maxSignalBuffer)
    : newSignal;
});
```

**Impact**:
- Previous patient's data remains in buffer
- New patient's data gets appended
- Chart shows mixed signals from different patients

#### 3. **Timestamp Processing Issues**
**Location**: `SimpleECGChart.tsx:203-208`

**Problem**: Timestamp processing doesn't account for patient switches:
- Uses absolute timestamps that may overlap between patients
- No timestamp offset or reset when switching patients
- Creates visual overlapping with time shifts

```typescript
latestPoints.forEach((point: ECGDataPoint) => {
  const timeInSeconds = point.timestamp / 1000; // ❌ Absolute time - no patient context
  labels.push(timeInSeconds);
  data.push(point.value * config.scale);
});
```

### 🔍 **Secondary Contributing Factors:**

#### 4. **Missing Patient Context in Data Processing**
**Location**: `SimpleECGChart.tsx:194-208`

**Problem**: Data processing doesn't filter by patient ID:
- Processes all signal data regardless of patient
- No patient validation in chart component
- Assumes all data belongs to current patient

```typescript
const processNewData = useCallback(() => {
  if (signal.length === 0) return;

  // ❌ No patientId validation
  const latestPoints = signal.slice(-config.maxDataPoints);
  // ❌ Processes all data, including other patients'
```

#### 5. **Chart.js Data Accumulation**
**Location**: `SimpleECGChart.tsx:210-226`

**Problem**: Chart.js data structures accumulate values:
- `labels` array grows continuously
- `data` array never gets cleared
- Only limited by maxDataPoints, not patient switches

```typescript
setChartData((prevData) => ({
  labels, // ❌ Completely replaces labels, but may contain old + new data
  datasets: [{
    ...prevData.datasets[0],
    data, // ❌ Accumulates all data points
  }],
}));
```

### 🔍 **Data Flow Analysis During Patient Switch:**

```
Patient A ECG Data → Signal Buffer → Chart Display
                    ↓
               Patient Switch (❌ No buffer clear)
                    ↓
Patient B ECG Data → Signal Buffer → Chart Display
                    ↓
              Mixed Data (❌ Patient A + Patient B)
                    ↓
           Chart Shows Overlapping Signals
```

### 🔍 **Specific Code Issues:**

#### Issue A: No Patient ID State Reset
```typescript
// ❌ MISSING in SimpleECGChart.tsx
useEffect(() => {
  // Should reset chart data when patientId changes
  setChartData({
    labels: [],
    datasets: [{
      label: "ECG Signal",
      data: [], // Clear old patient's data
      // ... reset other properties
    }],
  });
}, [patientId]); // This useEffect doesn't exist
```

#### Issue B: Shared Signal Buffer
```typescript
// ❌ PROBLEM in useECGStream.ts
// Signal buffer is shared across all chart instances
// No patient-specific isolation
const [signal, setSignal] = useState<ECGDataPoint[]>([]);
```

#### Issue C: No Patient Data Validation
```typescript
// ❌ MISSING data validation
const latestPoints = signal.filter(point =>
  point.patient_id === patientId // This filter doesn't exist
);
```

### 🔍 **Overlapping Pattern Analysis:**

Based on user description and code analysis:

1. **Time Shift Overlap**:
   - Previous patient's data uses old timestamps
   - New patient's data uses new timestamps
   - Chart displays both on same timeline
   - Creates slight offset between waveforms

2. **Amplitude Mixing**:
   - Different patients have different ECG amplitudes
   - Values get mixed in data arrays
   - Creates distorted waveform patterns

3. **Visual Effect**:
   - Multiple ECG patterns visible simultaneously
   - Ghost images of previous patient's signal
   - Unreadable medical display

## 🚨 **Critical Issues Summary:**

1. **No chart state reset on patient switch** (CRITICAL)
2. **Shared signal buffer between patients** (CRITICAL)
3. **Missing patient data filtering** (HIGH)
4. **Timestamp continuity not broken on patient switch** (HIGH)
5. **No patient context in data processing** (MEDIUM)

## 💡 **Recommended Solutions:**

### High Priority (Critical Fixes):
1. **Add patientId useEffect in SimpleECGChart**
   ```typescript
   useEffect(() => {
     // Clear all chart data when patient changes
     setChartData({
       labels: [],
       datasets: [{
         label: `ECG Signal - Patient ${patientId}`,
         data: [],
         // ... reset all properties
       }],
     });
   }, [patientId]);
   ```

2. **Filter signal data by patient ID**
   ```typescript
   const patientSpecificSignal = signal.filter(point =>
     point.patient_id === patientId
   );
   ```

3. **Clear signal buffer on patient switch**
   ```typescript
   useEffect(() => {
     setSignal([]); // Clear buffer when patient changes
   }, [patientId]);
   ```

### Medium Priority:
1. **Add patient-specific timestamps**
2. **Implement data validation checks**
3. **Add patient context to chart display**

### Low Priority:
1. **Add transition animations between patients**
2. **Implement patient-specific chart themes**
3. **Add patient metadata display**

## 📊 **Impact Assessment:**

- **Severity**: CRITICAL - Makes ECG unreadable
- **Frequency**: CONSISTENT - Occurs on every patient switch
- **User Impact**: CRITICAL - Compromises patient monitoring
- **Medical Risk**: HIGH - Could lead to misinterpretation

## 🔧 **Next Steps:**

1. **Immediate**: Implement patient switch state reset
2. **Urgent**: Add patient-specific data filtering
3. **Short-term**: Implement signal buffer management
4. **Medium-term**: Add patient context and validation
5. **Long-term**: Implement patient-specific chart configurations

## 🧪 **Testing Strategy:**

1. **Test patient switching scenarios**
2. **Verify data isolation between patients**
3. **Check chart state reset functionality**
4. **Validate timestamp handling**
5. **Test with multiple rapid patient switches**