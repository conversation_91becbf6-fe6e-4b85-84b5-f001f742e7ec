"""
Load MIT-BIH ECG recordings with expert annotations.

This script loads ECG signals and their corresponding expert annotations
from the MIT-BIH Arrhythmia Database and stores them in the database.

Usage:
    poetry run python scripts/load_mitbih_annotations.py
"""

import asyncio
import gzip
import json
import logging
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import numpy as np
import wfdb

from src.database import DatabaseManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# MIT-BIH annotation symbol mapping
# Based on: https://archive.physionet.org/physiobank/database/html/mitdbdir/tables.htm
ANNOTATION_MAP = {
    'N': ('Normal beat', False),
    'L': ('Left bundle branch block beat', True),
    'R': ('Right bundle branch block beat', True),
    'A': ('Atrial premature beat', True),
    'a': ('Aberrated atrial premature beat', True),
    'J': ('Nodal (junctional) premature beat', True),
    'S': ('Supraventricular premature beat', True),
    'V': ('Premature ventricular contraction', True),
    'F': ('Fusion of ventricular and normal beat', True),
    '[': ('Start of ventricular flutter/fibrillation', True),
    '!': ('Ventricular flutter wave', True),
    ']': ('End of ventricular flutter/fibrillation', True),
    'e': ('Atrial escape beat', True),
    'j': ('Nodal (junctional) escape beat', True),
    'E': ('Ventricular escape beat', True),
    '/': ('Paced beat', True),
    'f': ('Fusion of paced and normal beat', True),
    'x': ('Non-conducted P-wave (blocked APB)', True),
    'Q': ('Unclassifiable beat', True),
    '|': ('Isolated QRS-like artifact', True),
}

# Rhythm annotations
RHYTHM_MAP = {
    '(N': 'normal',
    '(AFIB': 'atrial_fibrillation',
    '(AFL': 'atrial_flutter',
    '(B': 'ventricular_bigeminy',
    '(T': 'ventricular_trigeminy',
    '(VT': 'ventricular_tachycardia',
    '(IVR': 'idioventricular_rhythm',
    '(AB': 'atrial_bigeminy',
    '(SVTA': 'supraventricular_tachycardia',
    '(PREX': 'pre_excitation',
}


class MITBIHLoader:
    """Loader for MIT-BIH Arrhythmia Database with annotations."""
    
    def __init__(self, db_manager: DatabaseManager, dataset_path: str = "datasets/mitdb"):
        self.db_manager = db_manager
        self.dataset_path = Path(dataset_path)
        
    async def load_recording(
        self,
        record_name: str,
        patient_id: str,
        lead_index: int = 0
    ) -> str:
        """
        Load a single MIT-BIH recording with annotations.

        Args:
            record_name: MIT-BIH record number (e.g., '100', '101')
            patient_id: Patient ID to assign this recording to
            lead_index: Which lead to use (0 = MLII, 1 = V5)

        Returns:
            Recording ID
        """
        logger.info(f"📥 Loading MIT-BIH record {record_name} for patient {patient_id}")

        # Load ECG signal (WFDB will auto-download if not present)
        # Using PhysioNet database name and record number
        try:
            record = wfdb.rdrecord(record_name, pn_dir='mitdb')
            signal = record.p_signal[:, lead_index]  # Select lead

            logger.info(f"   Signal: {len(signal)} samples at {record.fs} Hz")
            logger.info(f"   Duration: {len(signal) / record.fs:.1f} seconds")
            logger.info(f"   Lead: {record.sig_name[lead_index]}")

            # Load annotations (WFDB will auto-download if not present)
            annotation = wfdb.rdann(record_name, 'atr', pn_dir='mitdb')
        except Exception as e:
            logger.error(f"❌ Failed to load record {record_name}: {e}")
            raise
        logger.info(f"   Annotations: {len(annotation.sample)} beats")
        
        # Compress signal for storage
        compressed_signal = gzip.compress(signal.astype(np.float32).tobytes())
        compression_ratio = len(signal) * 4 / len(compressed_signal)
        logger.info(f"   Compressed: {len(compressed_signal)} bytes (ratio: {compression_ratio:.1f}x)")
        
        # Insert recording
        recording_id = str(uuid.uuid4())
        await self._insert_recording(
            recording_id=recording_id,
            patient_id=patient_id,
            signal_data=compressed_signal,
            sample_rate=int(record.fs),
            duration_seconds=len(signal) / record.fs,
            lead_type=record.sig_name[lead_index],
            metadata={
                'source': 'MIT-BIH',
                'record': record_name,
                'lead_index': lead_index,
                'num_beats': len(annotation.sample),
            }
        )
        
        # Insert beat-level annotations
        await self._insert_beat_annotations(
            recording_id=recording_id,
            annotation=annotation,
            sample_rate=int(record.fs)
        )
        
        # Create segment-level classifications (10-second windows)
        await self._create_segment_classifications(
            recording_id=recording_id,
            annotation=annotation,
            signal=signal,
            sample_rate=int(record.fs)
        )
        
        logger.info(f"✅ Successfully loaded record {record_name}")
        return recording_id
    
    async def _insert_recording(
        self,
        recording_id: str,
        patient_id: str,
        signal_data: bytes,
        sample_rate: int,
        duration_seconds: float,
        lead_type: str,
        metadata: Dict
    ) -> None:
        """Insert ECG recording into database."""
        import base64

        # Convert bytes to base64 string for text storage
        signal_data_b64 = base64.b64encode(signal_data).decode('utf-8')

        query = """
            INSERT INTO ecg_recordings (
                id, patient_id, recorded_at, duration_seconds,
                sample_rate, lead_type, signal_data, metadata
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        """

        await self.db_manager._execute_query(
            query,
            recording_id,
            patient_id,
            datetime.now(),
            duration_seconds,
            sample_rate,
            lead_type,
            signal_data_b64,
            json.dumps(metadata)
        )
    
    async def _insert_beat_annotations(
        self,
        recording_id: str,
        annotation: wfdb.Annotation,
        sample_rate: int
    ) -> None:
        """Insert individual beat annotations."""
        logger.info(f"   Inserting {len(annotation.sample)} beat annotations...")

        # Batch insert for better performance
        beat_count = 0
        for i, (sample, symbol) in enumerate(zip(annotation.sample, annotation.symbol)):
            # Skip non-beat annotations (rhythm markers, etc.)
            if symbol not in ANNOTATION_MAP:
                continue

            label, is_abnormal = ANNOTATION_MAP[symbol]

            query = """
                INSERT INTO ecg_annotations (
                    id, recording_id, sample_number, time_seconds,
                    annotation_type, annotation_label, confidence, is_abnormal
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            """

            try:
                await self.db_manager._execute_query(
                    query,
                    str(uuid.uuid4()),
                    recording_id,
                    int(sample),
                    float(sample / sample_rate),
                    symbol,
                    label,
                    1.0,  # Ground truth = 100% confidence
                    is_abnormal
                )
                beat_count += 1
            except Exception as e:
                logger.warning(f"   ⚠️  Failed to insert annotation at sample {sample}: {e}")
                continue

        logger.info(f"   ✅ {beat_count} beat annotations inserted")
    
    async def _create_segment_classifications(
        self,
        recording_id: str,
        annotation: wfdb.Annotation,
        signal: np.ndarray,
        sample_rate: int,
        segment_duration: float = 10.0
    ) -> None:
        """
        Create 10-second segment classifications based on annotations.
        
        Determines overall rhythm/classification for each segment.
        """
        total_duration = len(signal) / sample_rate
        num_segments = int(total_duration / segment_duration)
        
        logger.info(f"   Creating {num_segments} segment classifications...")
        
        for seg_idx in range(num_segments):
            start_time = seg_idx * segment_duration
            end_time = start_time + segment_duration
            start_sample = int(start_time * sample_rate)
            end_sample = int(end_time * sample_rate)
            
            # Find annotations in this segment
            segment_annotations = [
                (sample, symbol) 
                for sample, symbol in zip(annotation.sample, annotation.symbol)
                if start_sample <= sample < end_sample and symbol in ANNOTATION_MAP
            ]
            
            # Determine segment classification
            classification, is_abnormal = self._classify_segment(segment_annotations)

            query = """
                INSERT INTO ecg_segment_classifications (
                    id, recording_id, segment_start_time, segment_duration,
                    classification, confidence, is_abnormal, annotation_source
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            """

            try:
                await self.db_manager._execute_query(
                    query,
                    str(uuid.uuid4()),
                    recording_id,
                    start_time,
                    segment_duration,
                    classification,
                    1.0,  # Ground truth
                    is_abnormal,
                    'MIT-BIH'
                )
            except Exception as e:
                logger.warning(f"   ⚠️  Failed to insert classification for segment {seg_idx}: {e}")
                continue
        
        logger.info(f"   ✅ Segment classifications created")
    
    def _classify_segment(self, annotations: List[Tuple[int, str]]) -> Tuple[str, bool]:
        """
        Classify a segment based on beat annotations.
        
        Returns: (classification_label, is_abnormal)
        """
        if not annotations:
            return ('normal', False)
        
        symbols = [symbol for _, symbol in annotations]
        total_count = len(symbols)
        
        # Count different beat types
        normal_count = sum(1 for s in symbols if s == 'N')
        pvc_count = sum(1 for s in symbols if s == 'V')
        pac_count = sum(1 for s in symbols if s in ['A', 'a', 'J', 'S'])
        
        # Calculate ratios
        abnormal_ratio = (total_count - normal_count) / total_count if total_count > 0 else 0
        pvc_ratio = pvc_count / total_count if total_count > 0 else 0
        pac_ratio = pac_count / total_count if total_count > 0 else 0
        
        # Classify based on beat composition
        if abnormal_ratio == 0:
            return ('normal', False)
        elif pvc_ratio > 0.5:
            return ('ventricular_tachycardia', True)
        elif pac_ratio > 0.3:
            return ('atrial_fibrillation', True)
        elif pvc_ratio > 0.1:
            return ('frequent_pvc', True)
        elif pac_ratio > 0.1:
            return ('frequent_pac', True)
        elif abnormal_ratio > 0.05:
            return ('occasional_ectopy', True)
        else:
            return ('normal_with_rare_ectopy', False)


async def main():
    """Load sample MIT-BIH recordings."""
    from dotenv import load_dotenv
    load_dotenv()

    db_manager = DatabaseManager()
    await db_manager.create_pool()
    
    loader = MITBIHLoader(db_manager)
    
    # Load sample recordings
    # MIT-BIH records with different arrhythmias:
    # 100: Normal sinus rhythm
    # 106: Atrial fibrillation
    # 119: Ventricular tachycardia
    # 200: Frequent PVCs
    
    recordings = [
        ('100', 'patient-1'),  # Normal
        ('106', 'patient-2'),  # AFib
        ('119', 'patient-3'),  # VTach
        ('200', 'patient-4'),  # Frequent PVCs
    ]
    
    for record_name, patient_id in recordings:
        try:
            await loader.load_recording(record_name, patient_id)
        except Exception as e:
            logger.error(f"❌ Failed to load record {record_name}: {e}")
    
    await db_manager.close_pool()
    logger.info("🎉 All recordings loaded successfully!")


if __name__ == "__main__":
    asyncio.run(main())

