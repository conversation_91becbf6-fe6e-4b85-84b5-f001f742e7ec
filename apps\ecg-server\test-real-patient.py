#!/usr/bin/env python3

"""
Test WebSocket connection with a real patient ID from the database
"""

import asyncio
import websockets
import json
import sys

# Test parameters
ECG_SERVER_URL = "ws://localhost:8000"
# This is a real patient ID that would come from the database
REAL_PATIENT_ID = "QqGq_lQX8-IMQqSjE_fPV"  # From the logs
USER_ID = "Oaczetepf8kgsv5uiTJsW5Hi52dSL4BA"  # Dr<PERSON> <PERSON>'s user ID

async def test_real_patient():
    wsUrl = f"{ECG_SERVER_URL}/ws/ecg/{REAL_PATIENT_ID}?token={USER_ID}"
    
    print("🔧 Testing Real Patient WebSocket Connection:")
    print(f"   - Python version: {sys.version}")
    print(f"   - WebSocket library: websockets")
    print("")
    
    print("🔗 Testing WebSocket connection...")
    print(f"📍 URL: {wsUrl}")
    print(f"👤 User ID: {USER_ID}")
    print(f"🏥 Real Patient ID: {REAL_PATIENT_ID}")
    print("")
    
    try:
        # Connect to WebSocket
        async with websockets.connect(wsUrl, timeout=10) as websocket:
            print("✅ WebSocket connection opened successfully!")
            
            message_count = 0
            session_started = False
            
            # Listen for messages for 10 seconds
            async for message in websocket:
                try:
                    data = json.loads(message)
                    message_count += 1
                    
                    if data.get("type") == "session_start":
                        session_started = True
                        print(f"📨 Received message: {data['type']} - {data.get('timestamp', 'N/A')}")
                        print(f"🎯 Session started: {data.get('data', {}).get('session_id', 'N/A')}")
                    
                    elif data.get("type") == "signal":
                        if message_count <= 5 or message_count % 50 == 0:  # Show first 5 and every 50th
                            print(f"📨 Received message: {data['type']} - {data.get('timestamp', 'N/A')}")
                            print(f"📊 ECG signal: {data.get('data', {}).get('value', 'N/A')}")
                    
                    elif data.get("type") == "classification":
                        print(f"📨 Received message: {data['type']} - {data.get('timestamp', 'N/A')}")
                        result = data.get('data', {})
                        print(f"🧠 Classification: {result.get('class', 'N/A')} (confidence: {result.get('confidence', 'N/A')})")
                    
                    elif data.get("type") == "alert":
                        print(f"📨 Received message: {data['type']} - {data.get('timestamp', 'N/A')}")
                        print("🚨 ALERT: Abnormal ECG detected!")
                    
                    # Stop after 100 messages or 15 seconds
                    if message_count >= 100:
                        print(f"\n✅ Test completed successfully! Received {message_count} messages")
                        if session_started:
                            print("✅ Session started successfully")
                        break
                        
                except json.JSONDecodeError:
                    print(f"❌ Failed to parse message: {message}")
                except Exception as e:
                    print(f"❌ Error processing message: {e}")
                    
    except websockets.exceptions.ConnectionClosed as e:
        print(f"❌ WebSocket connection closed: {e}")
    except asyncio.TimeoutError:
        print("❌ WebSocket connection timeout")
    except Exception as e:
        print(f"❌ WebSocket connection failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_real_patient())
