# ECG Streaming API Reference

Quick reference guide for using the Patient Stream Registry API.

---

## Quick Start

### 1. Initialize Registry

```python
from src.database import DatabaseManager
from src.ecg.streams import PatientStreamRegistry, StreamConfig

# Create database manager
db_manager = DatabaseManager()
await db_manager.create_pool()

# Create registry with custom config
config = StreamConfig(
    sample_rate=360,      # Hz
    chunk_duration=0.25,  # seconds
    chunk_size=90,        # samples (auto-calculated)
    dtype="float32"
)

registry = PatientStreamRegistry(
    db_manager=db_manager,
    config=config,
    seed=42  # For deterministic mapping
)

# Initialize patients (fetches from database)
patient_count = await registry.initialize_patients()
print(f"Initialized {patient_count} patients")

# Or initialize specific patients
await registry.initialize_patients(["patient-1", "patient-2"])
```

### 2. Start Producers

```python
# Start all patient producers
await registry.start_all()

# Producers now run continuously in background
```

### 3. Subscribe to Patient Stream

```python
# Subscribe to a patient's ECG stream
patient_id = "test-patient-1"
subscriber_id = "websocket-connection-123"

stream = await registry.subscribe(patient_id, subscriber_id)

if stream is None:
    print(f"Patient {patient_id} not found")
else:
    # Iterate over ECG chunks
    async for chunk in stream:
        # chunk is numpy array of 90 float32 samples
        print(f"Received chunk: {len(chunk)} samples")
        
        # Send to WebSocket, process, etc.
        await websocket.send_bytes(chunk.tobytes())
```

### 4. Unsubscribe

```python
# Unsubscribe when done
await registry.unsubscribe(patient_id, subscriber_id)
```

### 5. Cleanup

```python
# Stop all producers
await registry.stop_all()

# Close database connection
await db_manager.close_pool()
```

---

## API Reference

### PatientStreamRegistry

Main registry managing all patient ECG producers.

#### Constructor

```python
PatientStreamRegistry(
    db_manager: DatabaseManager,
    config: Optional[StreamConfig] = None,
    seed: int = 42
)
```

**Parameters:**
- `db_manager`: Database manager for patient lookup
- `config`: Streaming configuration (uses defaults if None)
- `seed`: Random seed for deterministic patient-to-ECG mapping

#### Methods

##### `initialize_patients(patient_ids: Optional[List[str]] = None) -> int`

Initialize ECG sources and producers for patients.

```python
# Fetch all patients from database
count = await registry.initialize_patients()

# Or initialize specific patients
count = await registry.initialize_patients(["patient-1", "patient-2"])
```

**Returns:** Number of patients initialized

---

##### `start_all() -> None`

Start all patient producers.

```python
await registry.start_all()
```

Producers run continuously in background, generating chunks at real-time pace.

---

##### `stop_all() -> None`

Stop all patient producers.

```python
await registry.stop_all()
```

Gracefully stops all producers and clears subscriber queues.

---

##### `subscribe(patient_id: str, subscriber_id: str) -> Optional[AsyncIterator[np.ndarray]]`

Subscribe to a patient's ECG stream.

```python
stream = await registry.subscribe("patient-1", "subscriber-123")

if stream:
    async for chunk in stream:
        # Process chunk (90 samples)
        pass
```

**Parameters:**
- `patient_id`: Patient identifier
- `subscriber_id`: Unique subscriber identifier (e.g., WebSocket connection ID)

**Returns:** Async iterator yielding ECG chunks, or None if patient not found

---

##### `unsubscribe(patient_id: str, subscriber_id: str) -> None`

Unsubscribe from a patient's ECG stream.

```python
await registry.unsubscribe("patient-1", "subscriber-123")
```

**Parameters:**
- `patient_id`: Patient identifier
- `subscriber_id`: Subscriber identifier

---

##### `get_stats() -> Dict`

Get registry statistics.

```python
stats = registry.get_stats()
print(f"Total patients: {stats['total_patients']}")
print(f"Running producers: {stats['running_producers']}")
print(f"Total subscribers: {stats['total_subscribers']}")

# Per-producer stats
for patient_id, producer_stats in stats['producers'].items():
    print(f"{patient_id}: {producer_stats['chunks_sent']} chunks sent")
```

**Returns:**
```python
{
    "total_patients": int,
    "running_producers": int,
    "total_subscribers": int,
    "config": {
        "sample_rate": 360,
        "chunk_duration": 0.25,
        "chunk_size": 90,
        "dtype": "float32"
    },
    "producers": {
        "patient-1": {
            "patient_id": str,
            "patient_name": str,
            "condition": str,
            "is_running": bool,
            "subscribers": int,
            "chunks_sent": int,
            "uptime_seconds": float,
            "cursor_position": int,
            "signal_length": int
        },
        ...
    }
}
```

---

##### `get_patient_info(patient_id: str) -> Optional[Dict]`

Get information about a patient's ECG source.

```python
info = registry.get_patient_info("patient-1")
if info:
    print(f"Patient: {info['patient_name']}")
    print(f"Condition: {info['condition']}")
    print(f"Signal length: {info['signal_length']} samples")
    print(f"Streaming: {info['is_streaming']}")
    print(f"Subscribers: {info['subscribers']}")
```

**Returns:**
```python
{
    "patient_id": str,
    "patient_name": str,
    "condition": str,
    "description": str,
    "sample_rate": int,
    "signal_length": int,
    "is_streaming": bool,
    "subscribers": int
}
```

---

### StreamConfig

Configuration for ECG streaming.

```python
@dataclass
class StreamConfig:
    sample_rate: int = 360      # Hz
    chunk_duration: float = 0.25  # seconds
    chunk_size: int = 90        # samples (auto-calculated)
    dtype: str = "float32"
```

**Fields:**
- `sample_rate`: Sampling rate in Hz (default: 360)
- `chunk_duration`: Duration of each chunk in seconds (default: 0.25)
- `chunk_size`: Number of samples per chunk (auto-calculated from sample_rate * chunk_duration)
- `dtype`: Data type for samples (default: "float32")

---

### ECGSource

Represents an ECG data source for a patient.

```python
@dataclass
class ECGSource:
    patient_id: str
    signal: np.ndarray
    sample_rate: int
    condition: str
    patient_name: str
    description: str
```

---

## Usage Examples

### Example 1: WebSocket Handler

```python
from fastapi import WebSocket
from src.ecg.streams import PatientStreamRegistry

class ECGWebSocketHandler:
    def __init__(self, registry: PatientStreamRegistry):
        self.registry = registry
    
    async def handle_connection(self, websocket: WebSocket, patient_id: str):
        await websocket.accept()
        
        # Generate unique subscriber ID
        subscriber_id = f"ws-{id(websocket)}"
        
        try:
            # Subscribe to patient stream
            stream = await self.registry.subscribe(patient_id, subscriber_id)
            
            if stream is None:
                await websocket.close(code=1008, reason="Patient not found")
                return
            
            # Stream ECG chunks
            async for chunk in stream:
                # Send as binary
                await websocket.send_bytes(chunk.tobytes())
                
        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        finally:
            # Cleanup
            await self.registry.unsubscribe(patient_id, subscriber_id)
            await websocket.close()
```

### Example 2: Health Endpoint

```python
from fastapi import FastAPI

app = FastAPI()

@app.get("/health")
async def health_check():
    stats = registry.get_stats()
    
    return {
        "status": "healthy",
        "streaming": {
            "total_patients": stats["total_patients"],
            "running_producers": stats["running_producers"],
            "total_subscribers": stats["total_subscribers"],
            "config": stats["config"]
        }
    }
```

### Example 3: Patient Info Endpoint

```python
@app.get("/patients/{patient_id}/ecg-info")
async def get_patient_ecg_info(patient_id: str):
    info = registry.get_patient_info(patient_id)
    
    if info is None:
        raise HTTPException(status_code=404, detail="Patient not found")
    
    return info
```

### Example 4: Startup/Shutdown Lifecycle

```python
from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    db_manager = DatabaseManager()
    await db_manager.create_pool()
    
    registry = PatientStreamRegistry(db_manager)
    await registry.initialize_patients()
    await registry.start_all()
    
    # Store in app state
    app.state.registry = registry
    app.state.db_manager = db_manager
    
    yield
    
    # Shutdown
    await registry.stop_all()
    await db_manager.close_pool()

app = FastAPI(lifespan=lifespan)
```

---

## Performance Tips

1. **Reuse Registry**: Create one registry instance per application, not per request
2. **Subscriber IDs**: Use unique IDs (e.g., WebSocket connection ID) to avoid conflicts
3. **Cleanup**: Always unsubscribe when done to prevent memory leaks
4. **Monitoring**: Use `get_stats()` to monitor performance and detect issues
5. **Backpressure**: Monitor slow consumer warnings in logs

---

## Troubleshooting

### No chunks received

```python
# Check if patient exists
info = registry.get_patient_info(patient_id)
if info is None:
    print("Patient not found")

# Check if producer is running
if not info['is_streaming']:
    print("Producer not running")
    await registry.start_all()
```

### Slow consumer warnings

```
⚠️  Slow consumer detected: subscriber-123 for patient patient-1
```

**Solution**: Increase processing speed or implement frame dropping

### Memory usage growing

**Check**: Are you unsubscribing properly?

```python
# Always unsubscribe in finally block
try:
    async for chunk in stream:
        await process(chunk)
finally:
    await registry.unsubscribe(patient_id, subscriber_id)
```

---

## Testing

See `apps/ecg-server/test_streams.py` for comprehensive test examples.

```bash
cd apps/ecg-server
poetry run python test_streams.py
```

