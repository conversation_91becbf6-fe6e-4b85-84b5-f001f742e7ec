import { config } from "dotenv";
import { db } from "./index";
import { sql } from "drizzle-orm";
import { neonConfig } from "@neondatabase/serverless";

config();

// Configure Neon for better connectivity and timeout handling
neonConfig.poolQueryViaFetch = true;

async function clear() {
  console.log("🧹 Clearing database tables...");

  try {
    // Test connection first to wake up sleeping database
    console.log("🔌 Testing database connection...");
    await db.execute(sql`SELECT 1`);
    console.log("✅ Database connection established.");

    // Clear tables with timeout protection
    console.log("🧹 Clearing tables...");

    const clearOperation = db.execute(sql`
      TRUNCATE TABLE
        password_reset_tokens,
        organization_invitations,
        doctors,
        account,
        session,
        verification,
        "user",
        organizations
      RESTART IDENTITY CASCADE
    `);

    // Add 30-second timeout
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Clear operation timed out after 30 seconds')), 30000);
    });

    await Promise.race([clearOperation, timeoutPromise]);

    console.log("✅ All tables cleared successfully.");

  } catch (error: any) {
    console.error("❌ Failed to clear tables:", error.message);

    // Provide specific guidance based on error type
    if (error.message.includes('fetch failed') || error.message.includes('timeout')) {
      console.error("💡 Connection/timeout issue detected. Possible solutions:");
      console.error("   1. Check if Neon database is sleeping - visit Neon console to wake it");
      console.error("   2. Verify network connectivity");
      console.error("   3. Try running the command again (database may need time to wake up)");
      console.error("   4. Visit your Neon dashboard to ensure the database is active");
    } else if (error.message.includes('does not exist')) {
      console.error("💡 Table does not exist. Run 'pnpm db:push' to create tables first.");
    } else {
      console.error("💡 Unexpected error. Full details:", error);
    }

    process.exit(1);
  }
}

clear().then(() => process.exit(0));


