[tool.poetry]
name = "ecg-server"
version = "0.1.0"
description = "Real-time ECG streaming server for HealthLink"
authors = ["HealthLink Team"]
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.9"
fastapi = "^0.104.0"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
websockets = "^12.0"
numpy = "^1.24.0"
scipy = "^1.11.0"
wfdb = "^4.1.0"
asyncpg = "^0.29.0"
psycopg2-binary = "^2.9.7"
pydantic = "^2.5.0"
python-jose = "^3.3.0"
httpx = "^0.25.0"
python-dotenv = "^1.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.0"
black = "^23.0.0"
isort = "^5.12.0"
mypy = "^1.5.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py39']

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true