{"name": "ecg-server", "version": "0.1.0", "description": "FastAPI ECG streaming server for HealthLink", "scripts": {"dev": "poetry run uvicorn src.main:app --reload --host 0.0.0.0 --port 8000", "start": "poetry run uvicorn src.main:app --host 0.0.0.0 --port 8000", "install-deps": "poetry install", "lint": "black src/ && isort src/", "type-check": "mypy src/", "test": "pytest"}, "keywords": ["<PERSON><PERSON><PERSON>", "ecg", "streaming", "healthcare"], "private": true}