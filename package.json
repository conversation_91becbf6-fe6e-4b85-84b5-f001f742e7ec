{"name": "healthlink", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo dev", "build": "turbo build", "check-types": "turbo check-types", "dev:native": "turbo -F native dev", "dev:web": "turbo -F web dev", "dev:server": "turbo -F server dev", "dev:ecg": "turbo -F ecg-server dev", "db:push": "turbo -F server db:push", "db:studio": "turbo -F server db:studio", "db:generate": "turbo -F server db:generate", "db:migrate": "turbo -F server db:migrate"}, "dependencies": {}, "devDependencies": {"turbo": "^2.5.4"}, "packageManager": "pnpm@10.13.1"}