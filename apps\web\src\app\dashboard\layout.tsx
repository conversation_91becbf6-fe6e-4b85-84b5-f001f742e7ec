"use client";

import { usePathname, useRouter } from "next/navigation";
import { authClient } from "@/lib/auth-client";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Home,
  Activity,
  Users,
  FileText,
  Settings,
  LogOut,
  Stethoscope,
} from "lucide-react";

/**
 * Dashboard Layout Component
 *
 * Provides consistent navigation and layout for all dashboard pages
 */
export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const router = useRouter();
  const { data: session } = authClient.useSession();

  const handleSignOut = async () => {
    await authClient.signOut();
    router.push("/login");
  };

  const navigationItems = [
    {
      name: "Dashboard",
      href: "/dashboard",
      icon: Home,
      current: pathname === "/dashboard",
    },
    {
      name: "ECG Monitor",
      href: "/dashboard/ecg",
      icon: Activity,
      current: pathname === "/dashboard/ecg",
      badge: "Live",
    },
    {
      name: "Patients",
      href: "/dashboard/patients",
      icon: Users,
      current: pathname === "/dashboard/patients",
    },
    {
      name: "Reports",
      href: "/dashboard/reports",
      icon: FileText,
      current: pathname === "/dashboard/reports",
    },
    {
      name: "Settings",
      href: "/dashboard/settings",
      icon: Settings,
      current: pathname === "/dashboard/settings",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Navigation Bar */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            {/* Left side - Logo and Navigation */}
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <Stethoscope className="h-8 w-8 text-blue-600" />
                <span className="ml-2 text-xl font-bold text-gray-900">
                  HealthLink
                </span>
              </div>

              {/* Navigation Links */}
              <div className="hidden md:ml-8 md:flex md:space-x-1">
                {navigationItems.map((item) => {
                  const IconComponent = item.icon;
                  return (
                    <button
                      key={item.name}
                      onClick={() => {
                        console.log(
                          `🖱️ Navigation clicked: ${item.name} -> ${item.href}`
                        );
                        router.push(item.href);
                      }}
                      className={`
                        flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors
                        ${
                          item.current
                            ? "bg-blue-100 text-blue-700"
                            : "text-gray-700 hover:text-gray-900 hover:bg-gray-100"
                        }
                      `}
                    >
                      <IconComponent className="h-4 w-4 mr-2" />
                      {item.name}
                      {item.badge && (
                        <Badge className="ml-2 bg-red-500 text-xs">
                          {item.badge}
                        </Badge>
                      )}
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Right side - User info and logout */}
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">
                  Dr. {session?.user.name}
                </p>
                <p className="text-xs text-gray-700">{session?.user.email}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSignOut}
                className="flex items-center text-gray-900 hover:text-gray-900"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden border-t border-gray-200">
          <div className="px-2 pt-2 pb-3 space-y-1">
            {navigationItems.map((item) => {
              const IconComponent = item.icon;
              return (
                <button
                  key={item.name}
                  onClick={() => {
                    console.log(
                      `🖱️ Mobile Navigation clicked: ${item.name} -> ${item.href}`
                    );
                    router.push(item.href);
                  }}
                  className={`
                    flex items-center w-full px-3 py-2 rounded-md text-sm font-medium transition-colors
                    ${
                      item.current
                        ? "bg-blue-100 text-blue-700"
                        : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                    }
                  `}
                >
                  <IconComponent className="h-4 w-4 mr-2" />
                  {item.name}
                  {item.badge && (
                    <Badge className="ml-2 bg-red-500 text-xs">
                      {item.badge}
                    </Badge>
                  )}
                </button>
              );
            })}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="flex-1">{children}</main>

      {/* Footer */}
      <footer className="bg-white border-t">
        <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-700">
              © 2024 HealthLink Medical Systems. All rights reserved.
            </div>
            <div className="flex items-center space-x-4 text-sm text-gray-700">
              <span>
                ECG Server:{" "}
                <span className="text-green-600 font-semibold">Online</span>
              </span>
              <span>•</span>
              <span>
                Auth:{" "}
                <span className="text-green-600 font-semibold">Active</span>
              </span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
