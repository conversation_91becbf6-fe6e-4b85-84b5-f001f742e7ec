# ECG Streaming Implementation Plan - Updated for Real Data

**Date**: October 8, 2025  
**Status**: Updated to use real patient ECG recordings (no simulation)

---

## Summary of Changes

The implementation plan has been **updated to use real patient ECG data** instead of simulated/synthetic data.

### Key Updates:

1. **✅ Real ECG Recordings** - Load from `ecg_recordings` database table
2. **✅ ECGDataLoader** - Replaces `ECGSimulator` for production
3. **✅ ML-Based Classification** - Trained models instead of hard-coded conditions
4. **✅ Database Schema** - New tables for recordings and classifications
5. **❌ No Simulation** - Simulator only used as fallback in development

---

## Updated Implementation Steps

### M0: Database Schema (NEW - Priority 1)

**Status**: 🔄 **PENDING**

**What to do:**
1. Add ECG recording tables to `apps/server/src/db/schema/ecg.ts`
2. Create migration scripts
3. Load sample data from MIT-BIH or PhysioNet

**Files to create:**
- `apps/server/src/db/schema/ecg.ts` - Add `ecgRecordings` and `ecgClassifications` tables
- `apps/server/src/db/migrations/` - Migration scripts
- `apps/ecg-server/scripts/load_sample_ecg.py` - Load MIT-BIH data

**Success criteria:**
- Tables exist in database
- Sample recordings loaded (4-5 patients minimum)
- `SELECT COUNT(*) FROM ecg_recordings` returns > 0

---

### M1: Backend Producers + Subscribe API (UPDATED)

**Status**: ✅ **COMPLETED**

**What was done:**
1. Implemented `PatientStreamRegistry` in `apps/ecg-server/src/ecg/streams.py`
2. Implemented `ECGDataLoader` in `apps/ecg-server/src/ecg/data_loader.py`
3. Modified registry to use `ECGDataLoader` instead of `ECGSimulator`
4. Comprehensive test suite in `apps/ecg-server/test_streams.py`

**Changes from original plan:**
- Uses `ECGDataLoader.load_patient_recording()` to fetch real data
- Falls back to synthetic data only if no recordings exist
- No hard-coded condition mappings

**Success criteria:** ✅ All tests pass
- Deterministic patient-to-ECG mapping
- Precise timing (360 Hz, 0.25s chunks = 90 samples)
- Multiple subscribers receive identical data

---

### M2: WebSocket Subscription & Binary Frames

**Status**: 🔄 **PENDING**

**What to do:**
1. Modify `ECGWebSocketManager` to use `PatientStreamRegistry.subscribe()`
2. Implement binary frame transmission (Float32Array)
3. Send JSON header on connection: `{fs: 360, chunk_size: 90, dtype: 'float32'}`

**Changes from original plan:**
- Ensure real ECG data is streamed (not synthetic)
- Verify data source in logs

---

### M3: ML Classification Integration (NEW)

**Status**: 🔄 **PENDING**

**What to do:**
1. Load trained ML model on server startup
2. Update `ECGClassifier` to use real model (not hard-coded logic)
3. Classify 10-second segments in real-time
4. Store classifications in `ecg_classifications` table

**Files to modify:**
- `apps/ecg-server/src/ecg/classifier.py` - Load real ML model
- `apps/ecg-server/src/ecg/websocket_handler.py` - Use ML predictions

**Success criteria:**
- No `if mrn == "MRN001": condition = "normal"` logic
- All classifications from ML model
- Confidence scores included
- Classifications stored in database

---

### M4-M7: Frontend & Authorization

**Status**: 🔄 **PENDING** (No changes from original plan)

- M4: Authorization enforcement
- M5: Frontend patient list + default selection
- M6: Frontend WebSocket client hardening
- M7: Visualization & UX

---

## Files Created/Modified

### ✅ Completed

1. **`apps/ecg-server/src/ecg/streams.py`** (581 lines)
   - `PatientStreamRegistry` - Manages continuous producers
   - `PatientProducer` - Broadcasts chunks at real-time pace
   - `ECGSource` - Represents patient ECG data
   - `StreamConfig` - Streaming configuration

2. **`apps/ecg-server/src/ecg/data_loader.py`** (300 lines)
   - `ECGDataLoader` - Loads real recordings from database
   - `ECGRecording` - Dataclass for recording metadata
   - Compression/decompression support
   - Resampling for different sample rates

3. **`apps/ecg-server/test_streams.py`** (289 lines)
   - Comprehensive test suite for M1
   - All tests passing

4. **Documentation:**
   - `docs/real-ecg-data-architecture.md` - Complete architecture design
   - `docs/simulator-vs-real-data.md` - Transition guide
   - `docs/streaming-api-reference.md` - API documentation
   - `docs/milestone-m1-complete.md` - M1 completion report

### 🔄 Pending

1. **Database Schema** (M0)
   - `apps/server/src/db/schema/ecg.ts` - ECG recording tables
   - Migration scripts
   - Seed scripts for sample data

2. **ML Model Integration** (M3)
   - Update `apps/ecg-server/src/ecg/classifier.py`
   - Load trained model
   - Real-time classification

3. **WebSocket Integration** (M2)
   - Update `apps/ecg-server/src/ecg/websocket_handler.py`
   - Binary frame transmission
   - Subscribe to `PatientStreamRegistry`

---

## Environment Configuration

Add to `apps/ecg-server/.env`:

```bash
# ECG Data Source (for gradual migration)
USE_REAL_ECG_DATA=true   # true = database, false = simulator (development)
```

Update `apps/ecg-server/src/main.py`:

```python
import os

# Initialize registry with real data
use_real_data = os.getenv("USE_REAL_ECG_DATA", "false").lower() == "true"

registry = PatientStreamRegistry(
    db_manager=db_manager,
    use_real_data=use_real_data
)
```

---

## Next Steps (Priority Order)

1. **Week 1**: Implement M0 (Database Schema)
   - Add ECG recording tables
   - Create migration scripts
   - Load MIT-BIH sample data

2. **Week 2**: Integrate with PatientStreamRegistry
   - Set `USE_REAL_ECG_DATA=true`
   - Test with real recordings
   - Verify no simulation used

3. **Week 3**: Implement M3 (ML Classification)
   - Load trained model
   - Remove hard-coded conditions
   - Store classifications in database

4. **Week 4**: Complete M2 (WebSocket Integration)
   - Binary frame transmission
   - Subscribe to registry
   - End-to-end testing

---

## Testing Strategy

### Unit Tests
- ✅ `test_streams.py` - Producer timing, chunk size, multiple subscribers

### Integration Tests (To Add)
- Database schema creation
- ECG data loading from database
- ML model predictions
- WebSocket streaming with real data

### End-to-End Tests
- Dashboard loads real patients
- WebSocket streams real ECG data
- Classifications from ML model
- Patient switching works correctly

---

## Migration from Simulator to Real Data

### Current (Development)
```python
# Uses ECGSimulator
signal = self.ecg_simulator._generate_patient_specific_ecg(patient_profile)
condition = patient_profile.get("condition", "normal")  # Hard-coded
```

### Production (Real Data)
```python
# Uses ECGDataLoader
recording = await self.ecg_loader.load_patient_recording(patient_id)
signal = recording.signal  # Real data from database

# ML classification
classification = await self.classifier.classify_segment(signal)
condition = classification.condition  # From ML model
confidence = classification.confidence
```

---

## Success Criteria

### M0 (Database Schema)
- [ ] Tables created in Neon database
- [ ] Sample recordings loaded (4-5 patients)
- [ ] Can query recordings successfully

### M1 (Producers) ✅ COMPLETE
- [x] PatientStreamRegistry implemented
- [x] ECGDataLoader implemented
- [x] All tests passing
- [x] Real data support ready

### M2 (WebSocket)
- [ ] Binary frame transmission working
- [ ] Real ECG data streamed to clients
- [ ] Multiple clients receive identical data

### M3 (ML Classification)
- [ ] ML model loaded on startup
- [ ] Real-time classification working
- [ ] No hard-coded conditions
- [ ] Classifications stored in database

---

## References

- **Architecture**: `docs/real-ecg-data-architecture.md`
- **Transition Guide**: `docs/simulator-vs-real-data.md`
- **API Reference**: `docs/streaming-api-reference.md`
- **M1 Completion**: `docs/milestone-m1-complete.md`
- **Implementation Plan**: `docs/ecg-streaming-implementation-plan.md` (this file)

---

## Conclusion

The implementation plan has been **successfully updated** to use real patient ECG data:

✅ **M1 Complete** - Streaming architecture with real data support  
🔄 **M0 Pending** - Database schema for ECG recordings  
🔄 **M3 Pending** - ML model integration  

**No simulation needed for production** - All data comes from database recordings and ML model predictions.

