"""
Patient Stream Registry for ECG Real-Time Broadcasting

This module implements Milestone M1 of the ECG streaming architecture:
- Deterministic patient-to-ECG source mapping
- Continuous per-patient producers that run independently of WebSocket connections
- Broadcast channels for efficient multi-client streaming
- Real-time pacing at 360 Hz with configurable chunk sizes
"""

import asyncio
import logging
import random
from typing import Dict, Optional, AsyncIterator, List, Set
from dataclasses import dataclass
from datetime import datetime

import numpy as np

from .simulator import ECGSimulator
from ..database import DatabaseManager

logger = logging.getLogger(__name__)


@dataclass
class ECGSource:
    """Represents an ECG data source for a patient."""
    patient_id: str
    signal: np.ndarray
    sample_rate: int
    condition: str
    patient_name: str
    description: str
    classifications: List[Dict] = None  # Pre-loaded segment classifications
    annotations: List[Dict] = None  # Beat-level annotations (optional)


@dataclass
class StreamConfig:
    """Configuration for ECG streaming."""
    sample_rate: int = 360  # Hz
    chunk_duration: float = 0.25  # seconds
    chunk_size: int = 90  # samples per chunk (360 Hz * 0.25s)
    dtype: str = "float32"

    def __post_init__(self):
        """Validate and compute chunk size."""
        self.chunk_size = int(self.sample_rate * self.chunk_duration)


class PatientProducer:
    """
    Continuous producer for a single patient's ECG stream.

    Runs independently and broadcasts chunks to all subscribers at real-time pace.
    """

    def __init__(
        self,
        patient_id: str,
        ecg_source: ECGSource,
        config: StreamConfig
    ):
        """
        Initialize patient producer.

        Args:
            patient_id: Patient identifier
            ecg_source: ECG data source for this patient
            config: Streaming configuration
        """
        self.patient_id = patient_id
        self.ecg_source = ecg_source
        self.config = config

        # Broadcast channels (asyncio.Queue per subscriber)
        self.subscribers: Dict[str, asyncio.Queue] = {}
        self.subscriber_lock = asyncio.Lock()

        # Producer state
        self.is_running = False
        self.producer_task: Optional[asyncio.Task] = None
        self.cursor = 0  # Current position in signal
        self.current_time = 0.0  # Current playback time in seconds

        # Classification tracking
        self.last_classification_time = -999.0  # Last time a classification was sent
        self.classifications = ecg_source.classifications or []

        # Statistics
        self.chunks_sent = 0
        self.classifications_sent = 0
        self.start_time: Optional[datetime] = None

        logger.info(
            f"🫀 PatientProducer created for {ecg_source.patient_name} "
            f"({patient_id}, condition: {ecg_source.condition})"
        )

    async def start(self) -> None:
        """Start the continuous producer task."""
        if self.is_running:
            logger.warning(f"⚠️  Producer already running for patient {self.patient_id}")
            return

        self.is_running = True
        self.start_time = datetime.now()
        self.producer_task = asyncio.create_task(self._produce_loop())
        logger.info(f"▶️  Started producer for patient {self.patient_id}")

    async def stop(self) -> None:
        """Stop the producer task."""
        if not self.is_running:
            return

        self.is_running = False

        if self.producer_task:
            self.producer_task.cancel()
            try:
                await self.producer_task
            except asyncio.CancelledError:
                pass

        # Clear all subscriber queues
        async with self.subscriber_lock:
            for queue in self.subscribers.values():
                # Signal end of stream
                await queue.put(None)
            self.subscribers.clear()

        logger.info(f"⏹️  Stopped producer for patient {self.patient_id}")

    async def subscribe(self, subscriber_id: str) -> asyncio.Queue:
        """
        Subscribe to this patient's ECG stream.

        Args:
            subscriber_id: Unique identifier for the subscriber

        Returns:
            Queue that will receive ECG chunks
        """
        async with self.subscriber_lock:
            if subscriber_id in self.subscribers:
                logger.warning(
                    f"⚠️  Subscriber {subscriber_id} already subscribed to patient {self.patient_id}"
                )
                return self.subscribers[subscriber_id]

            # Create queue with reasonable buffer size (10 chunks = 2.5 seconds)
            queue = asyncio.Queue(maxsize=10)
            self.subscribers[subscriber_id] = queue

            logger.info(
                f"➕ Subscriber {subscriber_id} subscribed to patient {self.patient_id} "
                f"(total subscribers: {len(self.subscribers)})"
            )

            return queue

    async def unsubscribe(self, subscriber_id: str) -> None:
        """
        Unsubscribe from this patient's ECG stream.

        Args:
            subscriber_id: Subscriber identifier
        """
        async with self.subscriber_lock:
            if subscriber_id in self.subscribers:
                queue = self.subscribers.pop(subscriber_id)
                # Signal end of stream
                try:
                    await asyncio.wait_for(queue.put(None), timeout=1.0)
                except asyncio.TimeoutError:
                    pass

                logger.info(
                    f"➖ Subscriber {subscriber_id} unsubscribed from patient {self.patient_id} "
                    f"(remaining subscribers: {len(self.subscribers)})"
                )

    async def _produce_loop(self) -> None:
        """
        Main producer loop that generates and broadcasts ECG chunks.

        Runs continuously at real-time pace (360 Hz with 0.25s chunks).
        """
        signal_length = len(self.ecg_source.signal)
        chunk_interval = self.config.chunk_duration

        logger.info(
            f"🔄 Producer loop started for {self.patient_id} "
            f"(signal length: {signal_length} samples, chunk size: {self.config.chunk_size})"
        )

        try:
            while self.is_running:
                # Extract chunk from signal
                chunk_end = min(self.cursor + self.config.chunk_size, signal_length)
                chunk = self.ecg_source.signal[self.cursor:chunk_end].astype(np.float32)

                # If chunk is smaller than expected, wrap around
                if len(chunk) < self.config.chunk_size:
                    remaining = self.config.chunk_size - len(chunk)
                    wrap_chunk = self.ecg_source.signal[:remaining].astype(np.float32)
                    chunk = np.concatenate([chunk, wrap_chunk])
                    self.cursor = remaining
                else:
                    self.cursor = chunk_end

                # Wrap cursor if at end
                if self.cursor >= signal_length:
                    self.cursor = 0

                # Broadcast to all subscribers
                await self._broadcast_chunk(chunk)

                self.chunks_sent += 1

                # Check if we should send a classification
                classification = self._get_classification_at_time(self.current_time)
                if classification:
                    await self._broadcast_classification(classification)
                    self.classifications_sent += 1

                # Update current playback time
                self.current_time += chunk_interval

                # Log progress occasionally
                if self.chunks_sent % 100 == 0:
                    logger.debug(
                        f"📊 Patient {self.patient_id}: {self.chunks_sent} chunks sent, "
                        f"{self.classifications_sent} classifications sent, "
                        f"{len(self.subscribers)} active subscribers"
                    )

                # Sleep for chunk duration to maintain real-time pacing
                await asyncio.sleep(chunk_interval)

        except asyncio.CancelledError:
            logger.info(f"🛑 Producer loop cancelled for patient {self.patient_id}")
            raise
        except Exception as e:
            logger.error(f"❌ Producer loop error for patient {self.patient_id}: {e}")
            raise

    async def _broadcast_chunk(self, chunk: np.ndarray) -> None:
        """
        Broadcast chunk to all subscribers.

        Args:
            chunk: ECG data chunk to broadcast
        """
        if not self.subscribers:
            # No subscribers, skip broadcasting (but keep producing for timing)
            return

        async with self.subscriber_lock:
            # Create list of subscribers to avoid modification during iteration
            subscriber_items = list(self.subscribers.items())

        # Broadcast to all subscribers
        for subscriber_id, queue in subscriber_items:
            try:
                # Non-blocking put with timeout
                await asyncio.wait_for(queue.put(chunk.copy()), timeout=0.1)
            except asyncio.TimeoutError:
                logger.warning(
                    f"⚠️  Slow consumer detected: {subscriber_id} for patient {self.patient_id}"
                )
                # Could implement backpressure handling here (drop frames, etc.)
            except Exception as e:
                logger.error(
                    f"❌ Error broadcasting to subscriber {subscriber_id}: {e}"
                )

    def _get_classification_at_time(self, current_time: float) -> Optional[Dict]:
        """
        Get classification for current playback time.

        Args:
            current_time: Current playback time in seconds

        Returns:
            Classification dict if one should be sent, None otherwise
        """
        if not self.classifications:
            return None

        for classification in self.classifications:
            start = classification['segment_start_time']
            duration = classification['segment_duration']

            # Send classification at the start of each segment
            # Check if current time is within the first chunk of the segment
            if start <= current_time < start + self.config.chunk_duration:
                # Only send if we haven't sent this classification recently
                if current_time > self.last_classification_time + (duration * 0.9):
                    self.last_classification_time = current_time
                    return classification

        return None

    async def _broadcast_classification(self, classification: Dict) -> None:
        """
        Broadcast classification to all subscribers.

        Args:
            classification: Classification dict to broadcast
        """
        if not self.subscribers:
            return

        # Create classification message
        classification_msg = {
            'type': 'classification',
            'classification': classification['classification'],
            'confidence': classification['confidence'],
            'is_abnormal': classification['is_abnormal'],
            'segment_start_time': classification['segment_start_time'],
            'annotation_source': classification.get('annotation_source', 'unknown')
        }

        async with self.subscriber_lock:
            subscriber_items = list(self.subscribers.items())

        # Broadcast to all subscribers
        for subscriber_id, queue in subscriber_items:
            try:
                await asyncio.wait_for(queue.put(classification_msg), timeout=0.1)
            except asyncio.TimeoutError:
                logger.warning(
                    f"⚠️  Slow consumer for classification: {subscriber_id}"
                )
            except Exception as e:
                logger.error(
                    f"❌ Error broadcasting classification to {subscriber_id}: {e}"
                )

    def get_stats(self) -> Dict:
        """Get producer statistics."""
        uptime = (datetime.now() - self.start_time).total_seconds() if self.start_time else 0

        return {
            "patient_id": self.patient_id,
            "patient_name": self.ecg_source.patient_name,
            "condition": self.ecg_source.condition,
            "is_running": self.is_running,
            "subscribers": len(self.subscribers),
            "chunks_sent": self.chunks_sent,
            "classifications_sent": self.classifications_sent,
            "total_classifications": len(self.classifications),
            "uptime_seconds": uptime,
            "cursor_position": self.cursor,
            "signal_length": len(self.ecg_source.signal),
            "current_time": self.current_time,
        }


class PatientStreamRegistry:
    """
    Registry and manager for all patient ECG producers.

    Implements Milestone M1:
    - Deterministic patient-to-ECG mapping
    - Continuous producers for all patients
    - Subscribe API for WebSocket connections
    """

    def __init__(
        self,
        db_manager: DatabaseManager,
        config: Optional[StreamConfig] = None,
        seed: int = 42
    ):
        """
        Initialize the patient stream registry.

        Args:
            db_manager: Database manager for patient lookup
            config: Streaming configuration (uses defaults if None)
            seed: Random seed for deterministic patient-to-ECG mapping
        """
        self.db_manager = db_manager
        self.config = config or StreamConfig()
        self.seed = seed

        # Patient producers
        self.producers: Dict[str, PatientProducer] = {}

        # ECG simulator for generating patient-specific signals
        self.ecg_simulator = ECGSimulator(
            sample_rate=self.config.sample_rate,
            db_manager=db_manager
        )

        # Patient-to-ECG source mapping
        self.patient_ecg_mapping: Dict[str, ECGSource] = {}

        logger.info(
            f"🏥 PatientStreamRegistry initialized "
            f"(sample_rate: {self.config.sample_rate} Hz, "
            f"chunk_size: {self.config.chunk_size}, seed: {seed})"
        )

    async def initialize_patients(self, patient_ids: Optional[List[str]] = None) -> int:
        """
        Initialize ECG sources and producers for patients.

        Args:
            patient_ids: List of patient IDs to initialize. If None, fetches all from DB.

        Returns:
            Number of patients initialized
        """
        try:
            # Fetch patient IDs from database if not provided
            if patient_ids is None:
                patient_ids = await self._fetch_all_patient_ids()

            if not patient_ids:
                logger.warning("⚠️  No patients found to initialize")
                return 0

            logger.info(f"🔄 Initializing ECG sources for {len(patient_ids)} patients...")

            # Create deterministic patient-to-ECG mapping
            await self._create_patient_ecg_mapping(patient_ids)

            # Create producers for each patient
            for patient_id in patient_ids:
                if patient_id in self.patient_ecg_mapping:
                    ecg_source = self.patient_ecg_mapping[patient_id]
                    producer = PatientProducer(patient_id, ecg_source, self.config)
                    self.producers[patient_id] = producer

            logger.info(f"✅ Initialized {len(self.producers)} patient producers")
            return len(self.producers)

        except Exception as e:
            logger.error(f"❌ Error initializing patients: {e}")
            raise

    async def start_all(self) -> None:
        """Start all patient producers."""
        logger.info(f"▶️  Starting {len(self.producers)} patient producers...")

        for patient_id, producer in self.producers.items():
            try:
                await producer.start()
            except Exception as e:
                logger.error(f"❌ Failed to start producer for patient {patient_id}: {e}")

        logger.info(f"✅ All patient producers started")

    async def stop_all(self) -> None:
        """Stop all patient producers."""
        logger.info(f"⏹️  Stopping {len(self.producers)} patient producers...")

        for patient_id, producer in self.producers.items():
            try:
                await producer.stop()
            except Exception as e:
                logger.error(f"❌ Failed to stop producer for patient {patient_id}: {e}")

        logger.info(f"✅ All patient producers stopped")

    async def subscribe(self, patient_id: str, subscriber_id: str) -> Optional[AsyncIterator[np.ndarray]]:
        """
        Subscribe to a patient's ECG stream.

        Args:
            patient_id: Patient identifier
            subscriber_id: Unique subscriber identifier

        Returns:
            Async iterator yielding ECG chunks, or None if patient not found
        """
        if patient_id not in self.producers:
            logger.error(f"❌ Patient {patient_id} not found in registry")
            return None

        producer = self.producers[patient_id]
        queue = await producer.subscribe(subscriber_id)

        # Return async iterator that yields from queue
        return self._queue_iterator(queue, patient_id, subscriber_id)

    async def unsubscribe(self, patient_id: str, subscriber_id: str) -> None:
        """
        Unsubscribe from a patient's ECG stream.

        Args:
            patient_id: Patient identifier
            subscriber_id: Subscriber identifier
        """
        if patient_id in self.producers:
            await self.producers[patient_id].unsubscribe(subscriber_id)

    async def _queue_iterator(
        self,
        queue: asyncio.Queue,
        patient_id: str,
        subscriber_id: str
    ) -> AsyncIterator[np.ndarray]:
        """
        Convert queue to async iterator.

        Args:
            queue: Queue to iterate over
            patient_id: Patient identifier (for logging)
            subscriber_id: Subscriber identifier (for logging)

        Yields:
            ECG chunks from the queue
        """
        try:
            while True:
                chunk = await queue.get()

                # None signals end of stream
                if chunk is None:
                    logger.info(
                        f"🔚 End of stream for patient {patient_id}, subscriber {subscriber_id}"
                    )
                    break

                yield chunk

        except asyncio.CancelledError:
            logger.info(
                f"🛑 Stream cancelled for patient {patient_id}, subscriber {subscriber_id}"
            )
            raise
        except Exception as e:
            logger.error(
                f"❌ Error in stream iterator for patient {patient_id}, "
                f"subscriber {subscriber_id}: {e}"
            )
            raise

    async def _fetch_all_patient_ids(self) -> List[str]:
        """
        Fetch all patient IDs from the database.

        Returns:
            List of patient IDs
        """
        try:
            query = "SELECT id FROM patients ORDER BY created_at"
            rows = await self.db_manager._execute_fetch(query)
            patient_ids = [row['id'] for row in rows]

            logger.info(f"📋 Fetched {len(patient_ids)} patients from database")
            return patient_ids

        except Exception as e:
            logger.error(f"❌ Error fetching patient IDs: {e}")
            return []

    async def _create_patient_ecg_mapping(self, patient_ids: List[str]) -> None:
        """
        Create deterministic patient-to-ECG source mapping.

        Uses seeded random assignment to ensure reproducibility.

        Args:
            patient_ids: List of patient IDs to map
        """
        logger.info(f"🎲 Creating deterministic ECG mapping (seed: {self.seed})...")

        # Use seeded random for deterministic mapping
        rng = random.Random(self.seed)

        for patient_id in patient_ids:
            try:
                # Get patient profile from database
                patient_profile = await self.ecg_simulator._get_patient_profile(patient_id)

                # Generate patient-specific ECG signal
                signal = self.ecg_simulator._generate_patient_specific_ecg(patient_profile)

                # Create ECG source
                ecg_source = ECGSource(
                    patient_id=patient_id,
                    signal=signal,
                    sample_rate=self.config.sample_rate,
                    condition=patient_profile.get("condition", "normal"),
                    patient_name=patient_profile.get("name", f"Patient {patient_id}"),
                    description=patient_profile.get("description", "")
                )

                self.patient_ecg_mapping[patient_id] = ecg_source

                logger.debug(
                    f"✅ Mapped patient {patient_id} ({ecg_source.patient_name}) "
                    f"to {ecg_source.condition} ECG"
                )

            except Exception as e:
                logger.error(f"❌ Error mapping patient {patient_id}: {e}")

        logger.info(f"✅ Created {len(self.patient_ecg_mapping)} patient-ECG mappings")

    def get_stats(self) -> Dict:
        """
        Get registry statistics.

        Returns:
            Dictionary with registry stats
        """
        producer_stats = {
            patient_id: producer.get_stats()
            for patient_id, producer in self.producers.items()
        }

        total_subscribers = sum(
            stats["subscribers"] for stats in producer_stats.values()
        )

        running_producers = sum(
            1 for stats in producer_stats.values() if stats["is_running"]
        )

        return {
            "total_patients": len(self.producers),
            "running_producers": running_producers,
            "total_subscribers": total_subscribers,
            "config": {
                "sample_rate": self.config.sample_rate,
                "chunk_duration": self.config.chunk_duration,
                "chunk_size": self.config.chunk_size,
                "dtype": self.config.dtype,
            },
            "producers": producer_stats,
        }

    def get_patient_info(self, patient_id: str) -> Optional[Dict]:
        """
        Get information about a patient's ECG source.

        Args:
            patient_id: Patient identifier

        Returns:
            Dictionary with patient ECG info, or None if not found
        """
        if patient_id not in self.patient_ecg_mapping:
            return None

        ecg_source = self.patient_ecg_mapping[patient_id]
        producer = self.producers.get(patient_id)

        return {
            "patient_id": patient_id,
            "patient_name": ecg_source.patient_name,
            "condition": ecg_source.condition,
            "description": ecg_source.description,
            "sample_rate": ecg_source.sample_rate,
            "signal_length": len(ecg_source.signal),
            "is_streaming": producer.is_running if producer else False,
            "subscribers": len(producer.subscribers) if producer else 0,
        }
