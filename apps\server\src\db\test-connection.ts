import { config } from "dotenv";
import { neon, neonConfig } from "@neondatabase/serverless";
import { sql } from "drizzle-orm";

config();

// Configure Neon for better connectivity
neonConfig.poolQueryViaFetch = true;

async function testConnection() {
  console.log("🔌 Testing Neon database connection...");
  
  try {
    // Check environment variables
    console.log("📋 Environment check:");
    console.log("  DATABASE_URL exists:", !!process.env.DATABASE_URL);
    console.log("  Using pooler URL:", process.env.DATABASE_URL?.includes('-pooler') ? "✅" : "❌");
    
    // Test basic connection
    console.log("\n🔗 Testing connection...");
    const neonSql = neon(process.env.DATABASE_URL || "");
    
    // Add timeout wrapper
    const connectionTest = neonSql`SELECT 
      version() as db_version,
      current_database() as database_name,
      current_user as user_name,
      now() as current_time
    `;
    
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Connection test timed out after 10 seconds')), 10000);
    });
    
    const result = await Promise.race([connectionTest, timeoutPromise]);
    
    console.log("✅ Connection successful!");
    console.log("📊 Database info:");
    console.log("  Version:", result[0].db_version);
    console.log("  Database:", result[0].database_name);
    console.log("  User:", result[0].user_name);
    console.log("  Time:", result[0].current_time);
    
    // Test table listing
    console.log("\n📋 Checking tables...");
    const tablesResult = await neonSql`
      SELECT table_name, table_type
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    
    console.log("📊 Found", tablesResult.length, "tables:");
    tablesResult.forEach((table: any) => {
      console.log(`  - ${table.table_name} (${table.table_type})`);
    });
    
    console.log("\n✅ All tests passed! Database is accessible and healthy.");
    
  } catch (error: any) {
    console.error("❌ Connection test failed:", error.message);
    
    // Provide specific guidance
    if (error.message.includes('fetch failed') || error.message.includes('timeout')) {
      console.error("\n💡 Connection/timeout issue detected:");
      console.error("  1. Check if Neon database is sleeping - visit Neon console to wake it");
      console.error("  2. Verify network connectivity and DNS resolution");
      console.error("  3. Ensure DATABASE_URL is correctly formatted");
      console.error("  4. Try using the pooler URL (recommended for serverless)");
    } else if (error.message.includes('authentication')) {
      console.error("\n💡 Authentication issue:");
      console.error("  1. Verify DATABASE_URL credentials are correct");
      console.error("  2. Check if database user has proper permissions");
    } else {
      console.error("\n💡 Unexpected error. Full details:", error);
    }
    
    process.exit(1);
  }
}

testConnection().then(() => process.exit(0));
