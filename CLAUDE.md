# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Clinical Decision Support System (CDSS) for ECG monitoring and analysis. It's a monorepo built with Better T3 Stack using Turborepo for orchestration. 

The system provides:
- **Real-time ECG monitoring** with 10-second segment analysis at 360 Hz sampling rate
- **AI-powered arrhythmia detection** using machine learning models
- **Multi-platform access** for doctors (web) and patients (mobile)
- **Real-time alerts** via email, SMS, and in-app notifications
- **HIPAA-compliant data handling** with encryption at rest and in transit

### Applications:
- **Web app** (Next.js) - Doctor dashboard at port 3001
- **Server** (Next.js + tRPC) - Backend API at port 3000  
- **Native app** (React Native + Expo) - Patient mobile application

## Common Development Commands

### Starting Development

```bash
# Start all services
pnpm dev

# Start individual services
pnpm dev:web      # Web frontend only
pnpm dev:server   # Backend API only
pnpm dev:native   # Mobile app only
```

### Building and Type Checking

```bash
pnpm build         # Build all applications
pnpm check-types   # TypeScript type checking across all apps
```

### Linting

Individual apps have lint commands:
```bash
cd apps/web && pnpm lint        # Lint web application
```

### Database Operations (Drizzle ORM)

```bash
pnpm db:push      # Push schema changes to database (development)
pnpm db:generate  # Generate migration files
pnpm db:migrate   # Run migrations (production)
pnpm db:studio    # Open Drizzle Studio (database GUI)
```

### Package Management

Use pnpm workspaces. Navigate to specific app directories to add dependencies:

```bash
cd apps/web && pnpm add package-name      # Web-specific dependency
cd apps/server && pnpm add package-name   # Server-specific dependency
cd apps/native && pnpm add package-name   # Native-specific dependency
```

### Native App Development

```bash
cd apps/native
pnpm android      # Run on Android device/emulator
pnpm ios          # Run on iOS device/simulator
pnpm web          # Run native app in web browser
pnpm prebuild     # Generate native code for development builds
```

## Architecture Overview

### Technology Stack

- **Frontend**: Next.js 15 with App Router, Tailwind CSS, shadcn/ui
- **Backend**: Next.js API routes with tRPC for type-safe APIs
- **Database**: Neon PostgreSQL with Drizzle ORM
- **Authentication**: Better Auth (email/password)
- **Mobile**: React Native with Expo, NativeWind for styling
- **Build System**: Turborepo with pnpm workspaces

### Key Directories

#### Server (`apps/server/`)

- `src/db/schema/` - Drizzle database schemas (auth.ts, todo.ts)
- `src/routers/` - tRPC API route definitions
- `src/lib/` - Shared server utilities (auth, context, trpc setup)
- `drizzle.config.ts` - Drizzle configuration

#### Web (`apps/web/`)

- `src/app/` - Next.js App Router pages and layouts
- `src/components/ui/` - shadcn/ui components
- `src/lib/` - Client utilities and tRPC client setup
- `src/utils/trpc.ts` - tRPC client configuration

#### Native (`apps/native/`)

- `app/` - Expo Router file-based routing
- `components/` - Shared React Native components
- `lib/` - Native utilities and tRPC client setup

### Database Schema

Currently includes:
- **User authentication** tables (user, session, account, verification)
- **Patient** management (name, age, gender, contact info, unique patient ID)
- **Doctor** management (name, organization email, contact info, roles)
- **ECG data** storage (linked to patient IDs, 10-second segments)
- **Alert** system (abnormal classifications, acknowledgments)
- **Diagnosis** results and historical data

### tRPC API Structure

- Main router in `apps/server/src/routers/index.ts`
- **Patient** router with CRUD operations for patient management
- **Doctor** router with CRUD operations and role-based access
- **ECG** router for data streaming and historical retrieval
- **Alert** router for real-time notifications and acknowledgments
- **Auth** router with role-based access control (doctors vs patients)
- Protected and public procedures with appropriate authorization
- Type safety maintained across all three platforms

## Development Patterns

### Adding New Features

1. Define database schema in `apps/server/src/db/schema/`
2. Create tRPC router in `apps/server/src/routers/`
3. Add router to main app router
4. Implement UI in web and/or native apps using tRPC client
5. Use `pnpm db:push` to sync schema changes during development

### Authentication Flow

- **Doctor Authentication**: Organization email-based login for web app
- **Patient Authentication**: Username/password for mobile app
- Role-based access control (RBAC) with appropriate permissions
- Better Auth handles sessions across web and mobile
- Use `authClient` for authentication operations
- Protected procedures in tRPC check authentication and authorization automatically

### Styling

- **Web**: Tailwind CSS with shadcn/ui components
- **Native**: NativeWind (Tailwind for React Native)

## Environment Setup

- Server requires `.env` file with:
  - `DATABASE_URL` for Neon PostgreSQL connection
  - Authentication secrets for Better Auth
  - Email service configuration (for doctor alerts)
  - SMS service configuration (Twilio for alerts)
- Web app needs `NEXT_PUBLIC_SERVER_URL` pointing to server (typically http://localhost:3000)
- Native app shares authentication configuration with web

## Key Features Implementation

### Real-Time ECG Processing
- 10-second segment analysis at 360 Hz sampling rate
- Low-latency streaming (<100ms updates) for web dashboard
- ML model integration for arrhythmia detection
- Historical ECG data storage and retrieval

### Alert System
- In-app notifications for doctors (web dashboard)
- Email alerts to organization addresses
- SMS notifications via Twilio
- Alert acknowledgment and tracking

### Security & Compliance
- HTTPS for all communications
- Data encryption at rest and in transit
- HIPAA-compliant data handling
- Role-based access control for sensitive data

## React Best Practices Standards

The following React best practices MUST be followed for all component development in this project:

### Critical Standards (MUST ALWAYS FOLLOW)

1. **ID Generation**: ALWAYS use React's `useId()` hook, NEVER use `Math.random()` for ID generation to prevent SSR hydration mismatches.

2. **Separation of Concerns**: Extract business logic into custom hooks. Components should only handle UI rendering and user interactions.

3. **Performance Optimization**: Use `useCallback` for event handlers and `useMemo` for expensive calculations to prevent unnecessary re-renders.

4. **TypeScript Types**: All components must have comprehensive TypeScript interfaces with JSDoc documentation.

### Code Organization Standards

- **Custom Hooks**: Store in `src/hooks/` directory (e.g., `useSignIn`, `useForm`)
- **Type Definitions**: Store in `src/types/` directory (e.g., `auth.ts`, `api.ts`)  
- **Utility Functions**: Store in `src/utils/` directory (e.g., `error-handling.ts`)
- **Design System**: Use centralized constants from `src/constants/ui.ts`
- **Reusable Components**: Create in `src/components/ui/` (e.g., `FormField`, `FormSkeleton`)

### Component Structure Standards

```tsx
// Example component following best practices
import { useCallback } from "react";
import { useId } from "react";
import type { ComponentProps } from "@/types/component";
import { COLORS } from "@/constants/ui";

interface MyComponentProps {
  /** Description of the prop */
  label: string;
  /** Error message to display */
  error?: string;
}

export function MyComponent({ label, error }: MyComponentProps) {
  const id = useId();
  
  const handleClick = useCallback(() => {
    // Event handler logic
  }, []);

  return (
    // Component JSX
  );
}
```

### File Structure Requirements

```
src/
├── components/
│   ├── ui/           # Reusable UI components
│   └── [features]/   # Feature-specific components
├── hooks/            # Custom hooks for business logic
├── types/            # TypeScript type definitions
├── utils/            # Utility functions (error handling, etc.)
├── constants/        # Design system constants
└── lib/              # Third-party configurations
```

### Error Handling Standards

- Use typed error interfaces in `src/types/auth.ts`
- Use error utility functions from `src/utils/error-handling.ts`
- Always provide meaningful error messages to users
- Use proper TypeScript error types, not generic `any`

### Loading States & UX

- Always provide skeleton loading components for better perceived performance
- Use loading states from `src/components/ui/form-skeleton.tsx`
- Never leave users without visual feedback during async operations

### Accessibility Requirements

- Always use proper ARIA labels and semantic HTML
- Ensure proper focus management with focus rings
- Use `role="alert"` for error announcements
- Proper form labeling with `htmlFor` attributes

### Performance Requirements

- Extract event handlers with `useCallback`
- Memoize expensive calculations with `useMemo`
- Avoid inline object creation in render methods
- Use proper dependency arrays in hooks

These standards ensure consistent, maintainable, and performant React code across the entire project.
