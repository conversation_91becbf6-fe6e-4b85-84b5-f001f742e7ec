# ECG Streaming Implementation Plan for HealthLink

## Overview

This document outlines the comprehensive implementation plan for integrating real-time ECG signal streaming into our existing HealthLink project. The implementation will add a FastAPI-based ECG streaming server that communicates with our Next.js web application through WebSockets, while maintaining integration with our existing authentication and database systems.

## Implementation Phases

This plan is divided into **7 distinct phases** with verification checkpoints between each phase to ensure proper functionality before proceeding.

### Phase 1: Database Schema & Foundation Setup
**Duration**: 1-2 days  
**Verification**: Database tables created, migrations run successfully

### Phase 2: FastAPI Server Core Infrastructure
**Duration**: 2-3 days  
**Verification**: FastAPI server runs, health endpoint responds, basic authentication works

### Phase 3: ECG Data Simulation & Basic WebSocket
**Duration**: 2-3 days  
**Verification**: WebSocket connection established, sample ECG data streaming

### Phase 4: Frontend ECG Visualization
**Duration**: 2-3 days  
**Verification**: Real-time ECG chart displays streaming data from FastAPI server

### Phase 5: Authentication Integration & Authorization
**Duration**: 2-3 days  
**Verification**: Session validation works, role-based access enforced

### Phase 6: ECG Classification & Advanced Features
**Duration**: 3-4 days  
**Verification**: ML classification results displayed, alerts functioning

### Phase 7: Production Deployment & Testing
**Duration**: 2-3 days  
**Verification**: Full end-to-end testing, deployment configuration complete

---

## 1. Architecture Overview

### Current System Integration

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js Web   │    │   Next.js API   │    │   PostgreSQL    │
│   (Doctors)     │◄──►│   (tRPC/Auth)   │◄──►│   Database      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │
         │ WebSocket             │ HTTP/tRPC
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│   FastAPI ECG   │    │   Better Auth   │
│   Server        │    │   Session       │
└─────────────────┘    └─────────────────┘
```

### Key Integration Points

- **Authentication**: FastAPI server validates sessions through our existing Better Auth system
- **Database**: ECG metadata stored in PostgreSQL alongside existing user/doctor data
- **Real-time Communication**: WebSocket connection between FastAPI and Next.js
- **Role-based Access**: Doctors can only view ECG data for their assigned patients

## 2. FastAPI Server Implementation

### 2.1 Project Structure

```
apps/ecg-server/
├── pyproject.toml
├── requirements.txt
├── src/
│   ├── main.py
│   ├── auth/
│   │   ├── __init__.py
│   │   └── session_validator.py
│   ├── ecg/
│   │   ├── __init__.py
│   │   ├── simulator.py
│   │   ├── classifier.py
│   │   └── websocket_handler.py
│   ├── database/
│   │   ├── __init__.py
│   │   └── connection.py
│   └── models/
│       ├── __init__.py
│       └── schemas.py
└── datasets/
    └── sample_ecg_data/
```

### 2.2 Dependencies Setup

```toml
# apps/ecg-server/pyproject.toml
[tool.poetry]
name = "ecg-server"
version = "0.1.0"
description = "Real-time ECG streaming server for HealthLink"

[tool.poetry.dependencies]
python = "^3.9"
fastapi = "^0.104.0"
uvicorn = "^0.24.0"
websockets = "^12.0"
numpy = "^1.24.0"
scipy = "^1.11.0"
wfdb = "^4.1.0"
asyncpg = "^0.29.0"
pydantic = "^2.5.0"
python-jose = "^3.3.0"
httpx = "^0.25.0"
```

### 2.3 Core Implementation Files

#### Session Validation

```python
# apps/ecg-server/src/auth/session_validator.py
import httpx
from typing import Optional
from pydantic import BaseModel

class UserSession(BaseModel):
    user_id: str
    role: str
    email: str

class SessionValidator:
    def __init__(self, auth_server_url: str):
        self.auth_server_url = auth_server_url

    async def validate_session(self, session_token: str) -> Optional[UserSession]:
        """Validate session with Better Auth server"""
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{self.auth_server_url}/api/auth/session",
                headers={"Authorization": f"Bearer {session_token}"}
            )
            if response.status_code == 200:
                data = response.json()
                return UserSession(**data)
        return None
```

#### ECG Data Simulator

```python
# apps/ecg-server/src/ecg/simulator.py
import asyncio
import numpy as np
import wfdb
from typing import AsyncGenerator, Optional

class ECGSimulator:
    def __init__(self, sample_rate: int = 360):
        self.sample_rate = sample_rate
        self.interval = 1.0 / sample_rate

    async def stream_patient_ecg(
        self,
        patient_id: str,
        duration_seconds: Optional[int] = None
    ) -> AsyncGenerator[float, None]:
        """Stream ECG data for a specific patient"""
        # Load sample ECG data (in production, this would be patient-specific)
        signal = self._load_patient_data(patient_id)

        sample_count = 0
        max_samples = duration_seconds * self.sample_rate if duration_seconds else None

        while True:
            if max_samples and sample_count >= max_samples:
                break

            # Cycle through the signal data
            sample_index = sample_count % len(signal)
            yield float(signal[sample_index])

            sample_count += 1
            await asyncio.sleep(self.interval)

    def _load_patient_data(self, patient_id: str) -> np.ndarray:
        """Load ECG data for patient (placeholder implementation)"""
        # In production, load patient-specific ECG data
        # For now, use sample MIT-BIH data
        try:
            record = wfdb.rdrecord('datasets/sample_ecg_data/100')
            return record.p_signal[:, 0]  # First lead
        except:
            # Fallback to synthetic data
            t = np.linspace(0, 10, 3600)  # 10 seconds at 360 Hz
            return np.sin(2 * np.pi * 1.2 * t) + 0.1 * np.random.randn(len(t))
```

## 3. Frontend Integration

### 3.1 WebSocket Client Hook

```typescript
// apps/web/src/hooks/useECGStream.ts
"use client";
import { useEffect, useState, useCallback } from "react";
import { authClient } from "@/lib/auth-client";

interface ECGData {
  timestamp: number;
  value: number;
  patient_id: string;
}

interface ClassificationResult {
  class: string;
  confidence: number;
  timestamp: number;
  patient_id: string;
}

export function useECGStream(patientId: string) {
  const [signal, setSignal] = useState<ECGData[]>([]);
  const [classifications, setClassifications] = useState<
    ClassificationResult[]
  >([]);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const connectToStream = useCallback(async () => {
    try {
      const session = await authClient.getSession();
      if (!session) {
        setError("Authentication required");
        return;
      }

      const ws = new WebSocket(
        `${process.env.NEXT_PUBLIC_ECG_SERVER_URL}/ws/ecg/${patientId}?token=${session.token}`
      );

      ws.onopen = () => {
        setIsConnected(true);
        setError(null);
      };

      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);

        if (data.type === "signal") {
          setSignal((prev) => {
            const newData = [
              ...prev,
              {
                timestamp: Date.now(),
                value: data.value,
                patient_id: data.patient_id,
              },
            ];
            // Keep only last 10 seconds of data (3600 samples at 360Hz)
            return newData.slice(-3600);
          });
        } else if (data.type === "classification") {
          setClassifications((prev) => [
            ...prev,
            {
              ...data.result,
              timestamp: Date.now(),
              patient_id: data.patient_id,
            },
          ]);
        }
      };

      ws.onerror = () => {
        setError("WebSocket connection error");
        setIsConnected(false);
      };

      ws.onclose = () => {
        setIsConnected(false);
      };

      return () => {
        ws.close();
      };
    } catch (err) {
      setError("Failed to connect to ECG stream");
    }
  }, [patientId]);

  useEffect(() => {
    const cleanup = connectToStream();
    return () => cleanup?.then((fn) => fn?.());
  }, [connectToStream]);

  return { signal, classifications, isConnected, error };
}
```

### 3.2 ECG Visualization Component

```typescript
// apps/web/src/components/ECGChart.tsx
"use client";
import { useEffect, useRef } from "react";
import { useECGStream } from "@/hooks/useECGStream";

interface ECGChartProps {
  patientId: string;
  height?: number;
}

export default function ECGChart({ patientId, height = 400 }: ECGChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const { signal, isConnected, error } = useECGStream(patientId);

  useEffect(() => {
    if (!canvasRef.current || signal.length === 0) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw ECG waveform
    ctx.strokeStyle = "#10b981";
    ctx.lineWidth = 2;
    ctx.beginPath();

    const width = canvas.width;
    const height = canvas.height;
    const centerY = height / 2;
    const scale = height * 0.3; // Adjust scale as needed

    signal.forEach((point, index) => {
      const x = (index / signal.length) * width;
      const y = centerY - point.value * scale;

      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });

    ctx.stroke();

    // Draw grid lines
    ctx.strokeStyle = "#374151";
    ctx.lineWidth = 0.5;

    // Horizontal lines
    for (let i = 0; i <= 10; i++) {
      const y = (i / 10) * height;
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }

    // Vertical lines
    for (let i = 0; i <= 20; i++) {
      const x = (i / 20) * width;
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }
  }, [signal]);

  if (error) {
    return (
      <div className="flex items-center justify-center h-96 bg-red-50 border border-red-200 rounded-lg">
        <p className="text-red-600">Error: {error}</p>
      </div>
    );
  }

  return (
    <div className="relative">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-lg font-semibold">
          ECG Monitor - Patient {patientId}
        </h3>
        <div className="flex items-center space-x-2">
          <div
            className={`w-3 h-3 rounded-full ${
              isConnected ? "bg-green-500" : "bg-red-500"
            }`}
          />
          <span className="text-sm text-gray-600">
            {isConnected ? "Connected" : "Disconnected"}
          </span>
        </div>
      </div>
      <canvas
        ref={canvasRef}
        width={800}
        height={height}
        className="w-full border border-gray-300 rounded-lg bg-black"
      />
    </div>
  );
}
```

# Phase 1: Database Schema & Foundation Setup

## Phase 1 Overview
Set up the database foundation for ECG streaming functionality. This phase establishes the data structure needed for patients, ECG sessions, and classifications.

### Phase 1 Tasks:
1. Create ECG database schema files
2. Add new tables for patients, ECG sessions, and classifications  
3. Update database index exports
4. Run database migrations
5. Verify schema changes

### Phase 1 Verification Checklist:
- [ ] Database schema files created in `apps/server/src/db/schema/ecg.ts`
- [ ] All tables (patients, ecgSessions, ecgClassifications) exist in database
- [ ] Foreign key relationships working correctly
- [ ] Database indexes properly created
- [ ] `pnpm db:push` runs without errors
- [ ] `pnpm db:studio` shows new tables with correct structure

---

## 4. Database Schema Changes

### 4.1 New Tables

```typescript
// apps/server/src/db/schema/ecg.ts
import {
  pgTable,
  text,
  timestamp,
  boolean,
  integer,
  decimal,
  index,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import { user } from "./auth";
import { doctors } from "./doctors";

export const patients = pgTable(
  "patients",
  {
    id: text("id").primaryKey(),
    userId: text("user_id")
      .notNull()
      .references(() => user.id, { onDelete: "cascade" }),
    medicalRecordNumber: text("medical_record_number").unique(),
    dateOfBirth: timestamp("date_of_birth"),
    gender: text("gender", { enum: ["male", "female", "other"] }),
    emergencyContact: text("emergency_contact"),
    emergencyPhone: text("emergency_phone"),
    assignedDoctorId: text("assigned_doctor_id").references(() => doctors.id, {
      onDelete: "set null",
    }),
    createdAt: timestamp("created_at")
      .notNull()
      .default(sql`now()`),
    updatedAt: timestamp("updated_at")
      .notNull()
      .default(sql`now()`),
  },
  (table) => ({
    patientUserIdx: index("idx_patient_user_id").on(table.userId),
    patientDoctorIdx: index("idx_patient_doctor_id").on(table.assignedDoctorId),
  })
);

export const ecgSessions = pgTable(
  "ecg_sessions",
  {
    id: text("id").primaryKey(),
    patientId: text("patient_id")
      .notNull()
      .references(() => patients.id, { onDelete: "cascade" }),
    doctorId: text("doctor_id")
      .notNull()
      .references(() => doctors.id, { onDelete: "cascade" }),
    startTime: timestamp("start_time")
      .notNull()
      .default(sql`now()`),
    endTime: timestamp("end_time"),
    duration: integer("duration"), // in seconds
    sampleRate: integer("sample_rate").notNull().default(360),
    status: text("status", {
      enum: ["active", "completed", "interrupted"],
    }).default("active"),
    notes: text("notes"),
    createdAt: timestamp("created_at")
      .notNull()
      .default(sql`now()`),
    updatedAt: timestamp("updated_at")
      .notNull()
      .default(sql`now()`),
  },
  (table) => ({
    sessionPatientIdx: index("idx_session_patient_id").on(table.patientId),
    sessionDoctorIdx: index("idx_session_doctor_id").on(table.doctorId),
    sessionStatusIdx: index("idx_session_status").on(table.status),
  })
);

export const ecgClassifications = pgTable(
  "ecg_classifications",
  {
    id: text("id").primaryKey(),
    sessionId: text("session_id")
      .notNull()
      .references(() => ecgSessions.id, { onDelete: "cascade" }),
    timestamp: timestamp("timestamp")
      .notNull()
      .default(sql`now()`),
    classification: text("classification").notNull(),
    confidence: decimal("confidence", { precision: 5, scale: 4 }).notNull(),
    isAbnormal: boolean("is_abnormal").notNull().default(false),
    alertSent: boolean("alert_sent").notNull().default(false),
    createdAt: timestamp("created_at")
      .notNull()
      .default(sql`now()`),
  },
  (table) => ({
    classificationSessionIdx: index("idx_classification_session_id").on(
      table.sessionId
    ),
    classificationTimestampIdx: index("idx_classification_timestamp").on(
      table.timestamp
    ),
    classificationAbnormalIdx: index("idx_classification_abnormal").on(
      table.isAbnormal
    ),
  })
);
```

### 4.2 Update Database Index

```typescript
// apps/server/src/db/index.ts
import * as authSchema from "./schema/auth";
import * as organizationsSchema from "./schema/organizations";
import * as doctorsSchema from "./schema/doctors";
import * as ecgSchema from "./schema/ecg"; // Add this import

export const db = drizzle(sql, {
  schema: {
    ...authSchema,
    ...organizationsSchema,
    ...doctorsSchema,
    ...ecgSchema, // Add this
  },
});

// Re-export all schema tables for easy access
export * from "./schema/auth";
export * from "./schema/organizations";
export * from "./schema/doctors";
export * from "./schema/ecg"; // Add this export
```

---

# Phase 2: FastAPI Server Core Infrastructure

## Phase 2 Overview
Create the FastAPI server foundation with basic structure, dependencies, and authentication validation. This phase establishes the Python backend that will handle ECG streaming.

### Phase 2 Tasks:
1. Set up FastAPI project structure in `apps/ecg-server/`
2. Configure Python dependencies and virtual environment
3. Implement session validation with Better Auth
4. Create basic FastAPI application with health endpoint
5. Set up database connection for patient access verification

### Phase 2 Verification Checklist:
- [ ] FastAPI server starts successfully with `uvicorn src.main:app --reload`
- [ ] Health endpoint `/health` returns 200 OK
- [ ] Session validator can communicate with Better Auth server
- [ ] Database connection established and working
- [ ] Environment variables properly configured
- [ ] FastAPI server accessible at `http://localhost:8000`
- [ ] FastAPI auto-docs available at `http://localhost:8000/docs`

### Phase 2 Commands to Test:
```bash
cd apps/ecg-server
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows
pip install -r requirements.txt
uvicorn src.main:app --reload
curl http://localhost:8000/health
```

---

# Phase 3: ECG Data Simulation & Basic WebSocket

## Phase 3 Overview
Implement ECG data simulation and establish basic WebSocket connectivity. This phase creates the core streaming functionality without authentication complexity.

### Phase 3 Tasks:
1. Create ECG data simulator with sample data
2. Implement basic WebSocket endpoint
3. Set up WebSocket connection management
4. Test basic data streaming functionality
5. Add error handling for WebSocket connections

### Phase 3 Verification Checklist:
- [ ] ECG simulator generates realistic sample data
- [ ] WebSocket endpoint `/ws/ecg/{patient_id}` accepts connections
- [ ] WebSocket sends ECG data points at correct frequency (360 Hz)
- [ ] Connection management handles multiple clients
- [ ] WebSocket connection can be established from browser console
- [ ] Sample ECG data streams continuously
- [done ] WebSocket disconnection handled gracefully

### Phase 3 Test Commands:
```javascript
// Test in browser console
const ws = new WebSocket('ws://localhost:8000/ws/ecg/test-patient');
ws.onmessage = (event) => console.log('Received:', JSON.parse(event.data));
ws.onopen = () => console.log('Connected to ECG stream');
```

---

# Phase 4: Frontend ECG Visualization

## Phase 4 Overview
Create the React components and hooks needed to visualize streaming ECG data in the Next.js web application.

### Phase 4 Tasks:
1. Create `useECGStream` custom hook
2. Implement `ECGChart` component with canvas visualization
3. Set up real-time chart rendering
4. Add basic ECG dashboard page
5. Test WebSocket connection from frontend

### Phase 4 Verification Checklist:
- [ ] `useECGStream` hook successfully connects to WebSocket
- [ ] ECG chart displays real-time waveform data
- [ ] Canvas rendering updates smoothly (no flickering)
- [ ] Connection status indicator works correctly
- [ ] ECG dashboard page loads and displays chart
- [ ] Chart handles connection errors gracefully
- [ ] Real-time data visualization appears smooth and responsive

### Phase 4 Test Steps:
1. Start FastAPI server: `cd apps/ecg-server && uvicorn src.main:app --reload`
2. Start Next.js web: `cd apps/web && pnpm dev`
3. Visit `http://localhost:3001/dashboard/ecg`
4. Select a test patient and verify ECG chart displays
5. Check browser console for WebSocket connection status

---

# Phase 5: Authentication Integration & Authorization

## Phase 5 Overview
Integrate the FastAPI server with the existing Better Auth system and implement proper role-based access control for ECG streaming.

### Phase 5 Tasks:
1. Enhance session validation to check user roles
2. Implement patient access verification for doctors
3. Add authentication to WebSocket connections
4. Update frontend to pass authentication tokens
5. Test role-based access control

### Phase 5 Verification Checklist:
- [ ] WebSocket connections require valid session tokens
- [ ] Doctors can only access their assigned patients' ECG data
- [ ] Invalid authentication results in connection rejection
- [ ] Session expiration handled properly
- [ ] Frontend automatically includes auth tokens in WebSocket connections
- [ ] Authorization errors display meaningful messages to users
- [ ] Patient-doctor relationships enforced correctly

### Phase 5 Test Scenarios:
1. Login as doctor and verify access to assigned patients only
2. Try accessing ECG data for non-assigned patient (should fail)
3. Test with expired session (should reject connection)
4. Verify unauthenticated access is blocked

---

# Phase 6: ECG Classification & Advanced Features

## Phase 6 Overview
Add ML-based ECG classification, real-time alerts, and advanced visualization features.

### Phase 6 Tasks:
1. Implement ECG classification engine
2. Add classification results to WebSocket messages
3. Implement abnormality detection and alerting
4. Enhance ECG chart with classification displays
5. Add recording and session management features

### Phase 6 Verification Checklist:
- [ ] ECG classification runs on 10-second segments
- [ ] Classification results sent via WebSocket
- [ ] Abnormal rhythm detection triggers alerts
- [ ] Frontend displays classification results with confidence scores
- [ ] Alert system sends notifications for abnormal classifications
- [ ] ECG sessions properly created and tracked in database
- [ ] Enhanced chart controls (scale, speed) function correctly

### Phase 6 Test Cases:
1. Verify classification results appear every 10 seconds
2. Test abnormal rhythm detection and alert generation
3. Check classification confidence score display
4. Verify ECG session data stored in database

---

# Phase 7: Production Deployment & Testing

## Phase 7 Overview
Prepare the system for production deployment with comprehensive testing, Docker configuration, and environment setup.

### Phase 7 Tasks:
1. Create Docker configurations for FastAPI server
2. Update docker-compose for full system deployment
3. Implement comprehensive error handling and logging
4. Add health checks and monitoring
5. Run end-to-end testing suite

### Phase 7 Verification Checklist:
- [ ] Docker build successful for ECG server
- [ ] Full docker-compose stack runs correctly
- [ ] All services communicate properly in containerized environment
- [ ] Health checks pass for all services
- [ ] End-to-end tests pass completely
- [ ] Error handling covers edge cases
- [ ] Logging provides adequate debugging information
- [ ] Production environment variables configured

### Phase 7 Deployment Commands:
```bash
# Build and test Docker containers
docker-compose build
docker-compose up -d
docker-compose logs ecg-server

# Run end-to-end tests
cd apps/web && pnpm e2e:test
```

---

## 5. Authentication & Authorization

### 5.1 FastAPI Session Validation

```python
# apps/ecg-server/src/main.py
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Depends, HTTPException
from fastapi.security import HTTPBearer
from auth.session_validator import SessionValidator, UserSession
from ecg.websocket_handler import ECGWebSocketManager
import os

app = FastAPI(title="HealthLink ECG Streaming Server")
security = HTTPBearer()

# Initialize session validator
session_validator = SessionValidator(
    auth_server_url=os.getenv("AUTH_SERVER_URL", "http://localhost:3000")
)

# Initialize WebSocket manager
websocket_manager = ECGWebSocketManager()

async def get_current_user(token: str) -> UserSession:
    """Validate session and return user info"""
    user = await session_validator.validate_session(token)
    if not user:
        raise HTTPException(status_code=401, detail="Invalid session")
    return user

@app.websocket("/ws/ecg/{patient_id}")
async def websocket_endpoint(websocket: WebSocket, patient_id: str, token: str):
    # Validate session
    try:
        user = await get_current_user(token)
        if user.role not in ["doctor", "admin"]:
            await websocket.close(code=1008, reason="Insufficient permissions")
            return
    except:
        await websocket.close(code=1008, reason="Authentication failed")
        return

    # Check if doctor has access to this patient
    if not await websocket_manager.verify_patient_access(user.user_id, patient_id):
        await websocket.close(code=1008, reason="Access denied")
        return

    await websocket.accept()
    await websocket_manager.handle_connection(websocket, patient_id, user.user_id)
```

### 5.2 Patient Access Control

```python
# apps/ecg-server/src/ecg/websocket_handler.py
import asyncpg
from typing import Dict, Set
from fastapi import WebSocket
import os

class ECGWebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        self.db_url = os.getenv("DATABASE_URL")

    async def verify_patient_access(self, doctor_user_id: str, patient_id: str) -> bool:
        """Verify that the doctor has access to the patient's ECG data"""
        conn = await asyncpg.connect(self.db_url)
        try:
            # Check if doctor is assigned to this patient
            query = """
                SELECT COUNT(*) FROM patients p
                JOIN doctors d ON p.assigned_doctor_id = d.id
                WHERE p.id = $1 AND d.user_id = $2
            """
            result = await conn.fetchval(query, patient_id, doctor_user_id)
            return result > 0
        finally:
            await conn.close()

    async def handle_connection(self, websocket: WebSocket, patient_id: str, doctor_id: str):
        """Handle ECG streaming for a specific patient"""
        if patient_id not in self.active_connections:
            self.active_connections[patient_id] = set()

        self.active_connections[patient_id].add(websocket)

        try:
            # Start ECG streaming
            await self.stream_ecg_data(websocket, patient_id, doctor_id)
        except WebSocketDisconnect:
            self.active_connections[patient_id].discard(websocket)
            if not self.active_connections[patient_id]:
                del self.active_connections[patient_id]
```

## 6. Real-time Communication

### 6.1 WebSocket Protocol Design

```typescript
// packages/shared/src/ecg-types.ts
export interface ECGMessage {
  type: "signal" | "classification" | "session_start" | "session_end" | "error";
  timestamp: number;
  patient_id: string;
  data?: {
    value?: number;
    classification?: {
      class: string;
      confidence: number;
      is_abnormal: boolean;
    };
    session_id?: string;
    error_message?: string;
  };
}

export interface ECGStreamConfig {
  sample_rate: number;
  buffer_size: number;
  classification_interval: number; // seconds
}
```

### 6.2 Connection Management

```python
# apps/ecg-server/src/ecg/websocket_handler.py (continued)
from ecg.simulator import ECGSimulator
from ecg.classifier import ECGClassifier
import json
import asyncio
from datetime import datetime

class ECGWebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        self.db_url = os.getenv("DATABASE_URL")
        self.ecg_simulator = ECGSimulator()
        self.classifier = ECGClassifier()
        self.classification_buffer: Dict[str, list] = {}

    async def stream_ecg_data(self, websocket: WebSocket, patient_id: str, doctor_id: str):
        """Stream ECG data and handle classification"""
        # Create new ECG session
        session_id = await self.create_ecg_session(patient_id, doctor_id)

        # Send session start message
        await websocket.send_text(json.dumps({
            "type": "session_start",
            "timestamp": datetime.now().isoformat(),
            "patient_id": patient_id,
            "data": {"session_id": session_id}
        }))

        # Initialize classification buffer
        self.classification_buffer[session_id] = []

        try:
            async for sample in self.ecg_simulator.stream_patient_ecg(patient_id):
                # Send signal data
                message = {
                    "type": "signal",
                    "timestamp": datetime.now().isoformat(),
                    "patient_id": patient_id,
                    "data": {"value": sample}
                }
                await websocket.send_text(json.dumps(message))

                # Add to classification buffer
                self.classification_buffer[session_id].append(sample)

                # Perform classification every 10 seconds (3600 samples at 360Hz)
                if len(self.classification_buffer[session_id]) >= 3600:
                    await self.perform_classification(
                        websocket, session_id, patient_id,
                        self.classification_buffer[session_id][-3600:]
                    )

        except Exception as e:
            await websocket.send_text(json.dumps({
                "type": "error",
                "timestamp": datetime.now().isoformat(),
                "patient_id": patient_id,
                "data": {"error_message": str(e)}
            }))
        finally:
            # Clean up
            if session_id in self.classification_buffer:
                del self.classification_buffer[session_id]
            await self.end_ecg_session(session_id)

    async def perform_classification(self, websocket: WebSocket, session_id: str,
                                   patient_id: str, signal_segment: list):
        """Classify ECG segment and send results"""
        try:
            result = await self.classifier.classify_segment(signal_segment)

            # Store classification in database
            await self.store_classification(session_id, result)

            # Send classification result
            message = {
                "type": "classification",
                "timestamp": datetime.now().isoformat(),
                "patient_id": patient_id,
                "data": {
                    "classification": {
                        "class": result["class"],
                        "confidence": result["confidence"],
                        "is_abnormal": result["is_abnormal"]
                    }
                }
            }
            await websocket.send_text(json.dumps(message))

            # Send alert if abnormal
            if result["is_abnormal"]:
                await self.send_abnormality_alert(session_id, result)

        except Exception as e:
            print(f"Classification error: {e}")
```

## 7. Data Visualization

### 7.1 Enhanced ECG Chart Component

```typescript
// apps/web/src/components/ECGChart.tsx (enhanced version)
"use client";
import { useEffect, useRef, useState } from "react";
import { useECGStream } from "@/hooks/useECGStream";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface ECGChartProps {
  patientId: string;
  height?: number;
  showControls?: boolean;
}

export default function ECGChart({
  patientId,
  height = 400,
  showControls = true,
}: ECGChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const [isRecording, setIsRecording] = useState(false);
  const [scale, setScale] = useState(1);
  const [speed, setSpeed] = useState(1);

  const { signal, classifications, isConnected, error } =
    useECGStream(patientId);

  // Real-time animation
  useEffect(() => {
    if (!canvasRef.current || !isConnected) return;

    const animate = () => {
      drawECG();
      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [signal, scale, speed, isConnected]);

  const drawECG = () => {
    if (!canvasRef.current || signal.length === 0) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Clear canvas with black background
    ctx.fillStyle = "#000000";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw grid
    drawGrid(ctx, canvas.width, canvas.height);

    // Draw ECG waveform
    drawWaveform(ctx, canvas.width, canvas.height);

    // Draw real-time indicator
    drawTimeIndicator(ctx, canvas.width, canvas.height);
  };

  const drawGrid = (
    ctx: CanvasRenderingContext2D,
    width: number,
    height: number
  ) => {
    ctx.strokeStyle = "#1f2937";
    ctx.lineWidth = 0.5;

    // Major grid lines (every 5 small squares)
    ctx.strokeStyle = "#374151";
    for (let i = 0; i <= 20; i += 5) {
      const x = (i / 20) * width;
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }

    for (let i = 0; i <= 10; i += 2) {
      const y = (i / 10) * height;
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }

    // Minor grid lines
    ctx.strokeStyle = "#1f2937";
    for (let i = 0; i <= 20; i++) {
      const x = (i / 20) * width;
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }

    for (let i = 0; i <= 10; i++) {
      const y = (i / 10) * height;
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }
  };

  const drawWaveform = (
    ctx: CanvasRenderingContext2D,
    width: number,
    height: number
  ) => {
    if (signal.length < 2) return;

    ctx.strokeStyle = "#10b981";
    ctx.lineWidth = 2;
    ctx.beginPath();

    const centerY = height / 2;
    const amplitudeScale = height * 0.3 * scale;
    const timeScale = width / (signal.length * speed);

    signal.forEach((point, index) => {
      const x = index * timeScale;
      const y = centerY - point.value * amplitudeScale;

      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });

    ctx.stroke();
  };

  const drawTimeIndicator = (
    ctx: CanvasRenderingContext2D,
    width: number,
    height: number
  ) => {
    // Draw moving time indicator line
    ctx.strokeStyle = "#ef4444";
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(width - 2, 0);
    ctx.lineTo(width - 2, height);
    ctx.stroke();
  };

  const latestClassification = classifications[classifications.length - 1];

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">
            ECG Monitor - Patient {patientId}
          </CardTitle>
          <div className="flex items-center space-x-4">
            {latestClassification && (
              <Badge
                variant={
                  latestClassification.class === "normal"
                    ? "default"
                    : "destructive"
                }
                className="text-sm"
              >
                {latestClassification.class} (
                {(latestClassification.confidence * 100).toFixed(1)}%)
              </Badge>
            )}
            <div className="flex items-center space-x-2">
              <div
                className={`w-3 h-3 rounded-full ${
                  isConnected ? "bg-green-500" : "bg-red-500"
                }`}
              />
              <span className="text-sm text-gray-600">
                {isConnected ? "Live" : "Disconnected"}
              </span>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {error ? (
          <div className="flex items-center justify-center h-96 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600">Error: {error}</p>
          </div>
        ) : (
          <>
            <canvas
              ref={canvasRef}
              width={800}
              height={height}
              className="w-full border border-gray-300 rounded-lg"
            />

            {showControls && (
              <div className="flex items-center justify-between mt-4 p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <label className="text-sm font-medium">Scale:</label>
                    <input
                      type="range"
                      min="0.5"
                      max="3"
                      step="0.1"
                      value={scale}
                      onChange={(e) => setScale(parseFloat(e.target.value))}
                      className="w-20"
                    />
                    <span className="text-sm text-gray-600">
                      {scale.toFixed(1)}x
                    </span>
                  </div>

                  <div className="flex items-center space-x-2">
                    <label className="text-sm font-medium">Speed:</label>
                    <input
                      type="range"
                      min="0.5"
                      max="2"
                      step="0.1"
                      value={speed}
                      onChange={(e) => setSpeed(parseFloat(e.target.value))}
                      className="w-20"
                    />
                    <span className="text-sm text-gray-600">
                      {speed.toFixed(1)}x
                    </span>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Button
                    variant={isRecording ? "destructive" : "default"}
                    size="sm"
                    onClick={() => setIsRecording(!isRecording)}
                  >
                    {isRecording ? "Stop Recording" : "Start Recording"}
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
```

### 7.2 ECG Dashboard Page

```typescript
// apps/web/src/app/dashboard/ecg/page.tsx
"use client";
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { trpc } from "@/utils/trpc";
import ECGChart from "@/components/ECGChart";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { authClient } from "@/lib/auth-client";

export default function ECGDashboard() {
  const [selectedPatientId, setSelectedPatientId] = useState<string | null>(
    null
  );
  const { data: session } = authClient.useSession();

  // Get doctor's assigned patients
  const { data: patients, isLoading } = useQuery({
    queryKey: ["assigned-patients"],
    queryFn: () => trpc.patients.getAssignedPatients.query(),
    enabled: !!session?.user,
  });

  // Get recent ECG sessions
  const { data: recentSessions } = useQuery({
    queryKey: ["recent-ecg-sessions"],
    queryFn: () => trpc.ecg.getRecentSessions.query(),
    enabled: !!session?.user,
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">Loading...</div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">ECG Monitoring Dashboard</h1>
        <Badge variant="outline" className="text-sm">
          {patients?.length || 0} Assigned Patients
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Patient List */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle>Assigned Patients</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {patients?.map((patient) => (
              <div
                key={patient.id}
                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedPatientId === patient.id
                    ? "bg-blue-50 border-blue-200"
                    : "hover:bg-gray-50"
                }`}
                onClick={() => setSelectedPatientId(patient.id)}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">{patient.name}</p>
                    <p className="text-sm text-gray-600">
                      ID: {patient.medicalRecordNumber}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <span className="text-xs text-gray-500">Online</span>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* ECG Monitor */}
        <div className="lg:col-span-2">
          {selectedPatientId ? (
            <ECGChart patientId={selectedPatientId} height={400} />
          ) : (
            <Card className="h-96">
              <CardContent className="flex items-center justify-center h-full">
                <p className="text-gray-500">
                  Select a patient to view ECG monitoring
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Recent Sessions */}
      <Card>
        <CardHeader>
          <CardTitle>Recent ECG Sessions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Patient</th>
                  <th className="text-left p-2">Start Time</th>
                  <th className="text-left p-2">Duration</th>
                  <th className="text-left p-2">Status</th>
                  <th className="text-left p-2">Classifications</th>
                  <th className="text-left p-2">Actions</th>
                </tr>
              </thead>
              <tbody>
                {recentSessions?.map((session) => (
                  <tr key={session.id} className="border-b">
                    <td className="p-2">{session.patient.name}</td>
                    <td className="p-2">
                      {new Date(session.startTime).toLocaleString()}
                    </td>
                    <td className="p-2">
                      {session.duration ? `${session.duration}s` : "Ongoing"}
                    </td>
                    <td className="p-2">
                      <Badge
                        variant={
                          session.status === "active" ? "default" : "secondary"
                        }
                      >
                        {session.status}
                      </Badge>
                    </td>
                    <td className="p-2">{session._count.classifications}</td>
                    <td className="p-2">
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

## 8. Error Handling & Reconnection

### 8.1 Client-Side Reconnection Strategy

```typescript
// apps/web/src/hooks/useECGStream.ts (enhanced with reconnection)
export function useECGStream(patientId: string) {
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [maxReconnectAttempts] = useState(5);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();

  const connectWithRetry = useCallback(async () => {
    if (reconnectAttempts >= maxReconnectAttempts) {
      setError("Maximum reconnection attempts reached");
      return;
    }

    try {
      await connectToStream();
      setReconnectAttempts(0); // Reset on successful connection
    } catch (err) {
      setReconnectAttempts((prev) => prev + 1);
      const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000); // Exponential backoff

      reconnectTimeoutRef.current = setTimeout(() => {
        connectWithRetry();
      }, delay);
    }
  }, [patientId, reconnectAttempts, maxReconnectAttempts]);

  // Enhanced WebSocket with reconnection
  const connectToStream = useCallback(async () => {
    const session = await authClient.getSession();
    if (!session) {
      setError("Authentication required");
      return;
    }

    const ws = new WebSocket(
      `${process.env.NEXT_PUBLIC_ECG_SERVER_URL}/ws/ecg/${patientId}?token=${session.token}`
    );

    ws.onopen = () => {
      setIsConnected(true);
      setError(null);
      setReconnectAttempts(0);
    };

    ws.onclose = (event) => {
      setIsConnected(false);
      if (event.code !== 1000) {
        // Not a normal closure
        connectWithRetry();
      }
    };

    ws.onerror = () => {
      setError("WebSocket connection error");
      setIsConnected(false);
    };

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      ws.close(1000); // Normal closure
    };
  }, [patientId, connectWithRetry]);
}
```

### 8.2 Server-Side Error Handling

```python
# apps/ecg-server/src/ecg/websocket_handler.py (error handling)
import logging
from typing import Optional

logger = logging.getLogger(__name__)

class ECGWebSocketManager:
    async def handle_connection_with_recovery(self, websocket: WebSocket, patient_id: str, doctor_id: str):
        """Handle connection with comprehensive error recovery"""
        session_id = None
        try:
            session_id = await self.create_ecg_session(patient_id, doctor_id)
            await self.stream_ecg_data(websocket, patient_id, doctor_id, session_id)
        except WebSocketDisconnect:
            logger.info(f"WebSocket disconnected for patient {patient_id}")
        except Exception as e:
            logger.error(f"Error in ECG streaming for patient {patient_id}: {e}")
            try:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "timestamp": datetime.now().isoformat(),
                    "patient_id": patient_id,
                    "data": {"error_message": "Internal server error"}
                }))
            except:
                pass  # Connection might be closed
        finally:
            if session_id:
                await self.cleanup_session(session_id)
            self.remove_connection(patient_id, websocket)

    async def cleanup_session(self, session_id: str):
        """Clean up session data and mark as interrupted"""
        try:
            conn = await asyncpg.connect(self.db_url)
            await conn.execute(
                "UPDATE ecg_sessions SET status = 'interrupted', end_time = NOW() WHERE id = $1",
                session_id
            )
            await conn.close()
        except Exception as e:
            logger.error(f"Failed to cleanup session {session_id}: {e}")
```

## 9. Testing Strategy

### 9.1 Unit Tests for ECG Components

```typescript
// apps/web/src/components/__tests__/ECGChart.test.tsx
import { render, screen } from "@testing-library/react";
import { vi } from "vitest";
import ECGChart from "../ECGChart";

// Mock the useECGStream hook
vi.mock("@/hooks/useECGStream", () => ({
  useECGStream: vi.fn(() => ({
    signal: [
      { timestamp: Date.now(), value: 0.5, patient_id: "test-patient" },
      { timestamp: Date.now() + 1, value: 0.7, patient_id: "test-patient" },
    ],
    classifications: [],
    isConnected: true,
    error: null,
  })),
}));

describe("ECGChart", () => {
  it("renders ECG chart with patient ID", () => {
    render(<ECGChart patientId="test-patient" />);
    expect(
      screen.getByText(/ECG Monitor - Patient test-patient/)
    ).toBeInTheDocument();
  });

  it("shows connection status", () => {
    render(<ECGChart patientId="test-patient" />);
    expect(screen.getByText("Live")).toBeInTheDocument();
  });

  it("displays error message when connection fails", () => {
    vi.mocked(useECGStream).mockReturnValue({
      signal: [],
      classifications: [],
      isConnected: false,
      error: "Connection failed",
    });

    render(<ECGChart patientId="test-patient" />);
    expect(screen.getByText(/Error: Connection failed/)).toBeInTheDocument();
  });
});
```

### 9.2 Integration Tests for FastAPI

```python
# apps/ecg-server/tests/test_websocket.py
import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, patch
from src.main import app

@pytest.fixture
def client():
    return TestClient(app)

@pytest.fixture
def mock_session_validator():
    with patch('src.auth.session_validator.SessionValidator.validate_session') as mock:
        mock.return_value = AsyncMock(return_value={
            "user_id": "test-doctor",
            "role": "doctor",
            "email": "<EMAIL>"
        })
        yield mock

def test_websocket_connection_requires_auth(client):
    """Test that WebSocket connection requires authentication"""
    with client.websocket_connect("/ws/ecg/test-patient") as websocket:
        # Should close with authentication error
        assert websocket.close_code == 1008

def test_websocket_ecg_streaming(client, mock_session_validator):
    """Test ECG data streaming through WebSocket"""
    with patch('src.ecg.websocket_handler.ECGWebSocketManager.verify_patient_access', return_value=True):
        with client.websocket_connect("/ws/ecg/test-patient?token=valid-token") as websocket:
            # Should receive session start message
            data = websocket.receive_json()
            assert data["type"] == "session_start"
            assert data["patient_id"] == "test-patient"

            # Should receive signal data
            data = websocket.receive_json()
            assert data["type"] == "signal"
            assert "value" in data["data"]
```

### 9.3 End-to-End Testing

```typescript
// apps/web/src/e2e/ecg-streaming.spec.ts
import { test, expect } from "@playwright/test";

test.describe("ECG Streaming", () => {
  test.beforeEach(async ({ page }) => {
    // Login as doctor
    await page.goto("/login");
    await page.fill("[data-testid=email]", "<EMAIL>");
    await page.fill("[data-testid=password]", "password123");
    await page.click("[data-testid=login-button]");
    await page.waitForURL("/dashboard");
  });

  test("should display ECG dashboard and connect to patient stream", async ({
    page,
  }) => {
    await page.goto("/dashboard/ecg");

    // Should show ECG dashboard
    await expect(page.locator("h1")).toContainText("ECG Monitoring Dashboard");

    // Select a patient
    await page.click("[data-testid=patient-john-smith]");

    // Should show ECG chart
    await expect(page.locator("[data-testid=ecg-chart]")).toBeVisible();

    // Should show connection status
    await expect(page.locator("[data-testid=connection-status]")).toContainText(
      "Live"
    );

    // Should receive ECG data (wait for canvas to update)
    await page.waitForTimeout(2000);
    const canvas = page.locator("canvas");
    await expect(canvas).toBeVisible();
  });

  test("should handle connection errors gracefully", async ({ page }) => {
    // Mock WebSocket to fail
    await page.route("**/ws/ecg/**", (route) => route.abort());

    await page.goto("/dashboard/ecg");
    await page.click("[data-testid=patient-john-smith]");

    // Should show error message
    await expect(page.locator("[data-testid=error-message]")).toBeVisible();
  });
});
```

## 10. Deployment Considerations

### 10.1 Docker Configuration

```dockerfile
# apps/ecg-server/Dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY src/ ./src/
COPY datasets/ ./datasets/

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

# Run application
CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 10.2 Docker Compose Update

```yaml
# docker-compose.yml (updated)
version: "3.8"
services:
  web:
    build: ./apps/web
    ports:
      - "3001:3000"
    environment:
      - NEXT_PUBLIC_SERVER_URL=http://server:3000
      - NEXT_PUBLIC_ECG_SERVER_URL=ws://ecg-server:8000
    depends_on:
      - server
      - ecg-server

  server:
    build: ./apps/server
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - BETTER_AUTH_SECRET=${BETTER_AUTH_SECRET}
    depends_on:
      - postgres

  ecg-server:
    build: ./apps/ecg-server
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - AUTH_SERVER_URL=http://server:3000
    depends_on:
      - postgres
    volumes:
      - ./apps/ecg-server/datasets:/app/datasets

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=healthlink
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

### 10.3 Environment Variables

```bash
# .env.example (updated)
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/healthlink
POSTGRES_USER=healthlink_user
POSTGRES_PASSWORD=secure_password

# Authentication
BETTER_AUTH_SECRET=your-secret-key
BETTER_AUTH_URL=http://localhost:3000

# ECG Server
ECG_SERVER_URL=http://localhost:8000
AUTH_SERVER_URL=http://localhost:3000

# Frontend
NEXT_PUBLIC_SERVER_URL=http://localhost:3000
NEXT_PUBLIC_ECG_SERVER_URL=ws://localhost:8000
```

### 10.4 Production Deployment

```yaml
# kubernetes/ecg-server-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ecg-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ecg-server
  template:
    metadata:
      labels:
        app: ecg-server
    spec:
      containers:
        - name: ecg-server
          image: healthlink/ecg-server:latest
          ports:
            - containerPort: 8000
          env:
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: database-secret
                  key: url
          resources:
            requests:
              memory: "512Mi"
              cpu: "250m"
            limits:
              memory: "1Gi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /health
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: 8000
            initialDelaySeconds: 5
            periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: ecg-server-service
spec:
  selector:
    app: ecg-server
  ports:
    - port: 8000
      targetPort: 8000
  type: ClusterIP
```

## Quick Start Guide - Phase Implementation

To implement this plan phase by phase:

### 1. Before Starting
```bash
# Ensure you have the base HealthLink project running
pnpm dev  # Start existing services
pnpm db:studio  # Verify database access
```

### 2. Phase Implementation Order
Each phase must be completed and verified before proceeding to the next:

**Phase 1** → Database schema setup → **Test**: Verify tables exist  
**Phase 2** → FastAPI server foundation → **Test**: Health endpoint responds  
**Phase 3** → Basic WebSocket streaming → **Test**: Browser console WebSocket connection  
**Phase 4** → Frontend visualization → **Test**: ECG chart displays data  
**Phase 5** → Authentication integration → **Test**: Role-based access works  
**Phase 6** → ML classification & alerts → **Test**: Classifications appear  
**Phase 7** → Production deployment → **Test**: Full end-to-end functionality  

### 3. Key Verification Commands by Phase
```bash
# Phase 1: Database
pnpm db:push && pnpm db:studio

# Phase 2: FastAPI Server  
cd apps/ecg-server && uvicorn src.main:app --reload
curl http://localhost:8000/health

# Phase 3: WebSocket
# Test in browser: new WebSocket('ws://localhost:8000/ws/ecg/test')

# Phase 4: Frontend
cd apps/web && pnpm dev
# Visit: http://localhost:3001/dashboard/ecg

# Phase 5: Auth Integration
# Login as doctor and test patient access

# Phase 6: ML Features
# Verify classifications appear every 10 seconds

# Phase 7: Production
docker-compose up -d
```

### 4. Common Issues & Solutions
- **CORS errors**: Ensure FastAPI CORS middleware configured
- **WebSocket connection fails**: Check firewall/proxy settings
- **Authentication errors**: Verify Better Auth session format
- **Database errors**: Ensure foreign keys properly set up
- **Performance issues**: Check ECG data buffer sizes

---

This comprehensive implementation plan provides all the necessary components to integrate real-time ECG streaming into the HealthLink project while maintaining security, scalability, and seamless integration with the existing authentication and database systems.
