"""
Test classification broadcasting in PatientProducer.

This test verifies that classifications are broadcast at the correct times
during ECG streaming.
"""

import asyncio
import logging
import numpy as np
from src.ecg.streams import PatientProducer, ECGSource, StreamConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_classification_broadcasting():
    """Test that classifications are broadcast at segment boundaries."""
    
    logger.info("=" * 80)
    logger.info("TEST: Classification Broadcasting")
    logger.info("=" * 80)
    
    # Create mock ECG signal (30 seconds at 360 Hz = 10,800 samples)
    duration_seconds = 30.0
    sample_rate = 360
    total_samples = int(duration_seconds * sample_rate)
    signal = np.sin(np.linspace(0, 10 * np.pi, total_samples)).astype(np.float32)
    
    # Create mock classifications (3 segments of 10 seconds each)
    classifications = [
        {
            'segment_start_time': 0.0,
            'segment_duration': 10.0,
            'classification': 'normal',
            'confidence': 1.0,
            'is_abnormal': False,
            'annotation_source': 'TEST'
        },
        {
            'segment_start_time': 10.0,
            'segment_duration': 10.0,
            'classification': 'atrial_fibrillation',
            'confidence': 1.0,
            'is_abnormal': True,
            'annotation_source': 'TEST'
        },
        {
            'segment_start_time': 20.0,
            'segment_duration': 10.0,
            'classification': 'normal',
            'confidence': 1.0,
            'is_abnormal': False,
            'annotation_source': 'TEST'
        },
    ]
    
    # Create ECG source with classifications
    ecg_source = ECGSource(
        patient_id="test-patient",
        signal=signal,
        sample_rate=sample_rate,
        condition="test",
        patient_name="Test Patient",
        description="Test ECG with classifications",
        classifications=classifications
    )
    
    # Create stream config
    config = StreamConfig(sample_rate=360, chunk_duration=0.25)
    
    # Create producer
    producer = PatientProducer("test-patient", ecg_source, config)
    
    # Start producer
    await producer.start()
    
    # Subscribe to stream
    subscriber_id = "test-subscriber"
    queue = await producer.subscribe(subscriber_id)
    
    logger.info(f"\n📊 Test Setup:")
    logger.info(f"   Signal duration: {duration_seconds}s")
    logger.info(f"   Total samples: {total_samples}")
    logger.info(f"   Classifications: {len(classifications)}")
    logger.info(f"   Expected classifications to receive: 3")
    
    # Collect messages for 30 seconds
    received_classifications = []
    received_chunks = 0
    start_time = asyncio.get_event_loop().time()
    
    logger.info(f"\n🎬 Starting to receive messages...")
    
    try:
        while True:
            # Get message from queue (with timeout)
            try:
                message = await asyncio.wait_for(queue.get(), timeout=1.0)
                
                if message is None:
                    break
                
                # Check if it's a classification or signal chunk
                if isinstance(message, dict) and message.get('type') == 'classification':
                    received_classifications.append(message)
                    elapsed = asyncio.get_event_loop().time() - start_time
                    logger.info(
                        f"   ✅ Classification received at {elapsed:.1f}s: "
                        f"{message['classification']} "
                        f"(abnormal: {message['is_abnormal']})"
                    )
                else:
                    received_chunks += 1
                
                # Stop after 30 seconds
                if asyncio.get_event_loop().time() - start_time > 30.5:
                    break
                    
            except asyncio.TimeoutError:
                # Check if we've been running long enough
                if asyncio.get_event_loop().time() - start_time > 30.5:
                    break
                continue
                
    finally:
        # Cleanup
        await producer.unsubscribe(subscriber_id)
        await producer.stop()
    
    # Verify results
    logger.info(f"\n📈 Test Results:")
    logger.info(f"   Signal chunks received: {received_chunks}")
    logger.info(f"   Classifications received: {len(received_classifications)}")
    logger.info(f"   Expected classifications: 3")
    
    # Check classifications
    logger.info(f"\n🔍 Classification Details:")
    for i, cls in enumerate(received_classifications):
        logger.info(
            f"   {i+1}. {cls['classification']} "
            f"(start: {cls['segment_start_time']}s, "
            f"abnormal: {cls['is_abnormal']})"
        )
    
    # Assertions
    assert len(received_classifications) == 3, \
        f"Expected 3 classifications, got {len(received_classifications)}"
    
    assert received_classifications[0]['classification'] == 'normal', \
        "First classification should be 'normal'"
    
    assert received_classifications[1]['classification'] == 'atrial_fibrillation', \
        "Second classification should be 'atrial_fibrillation'"
    
    assert received_classifications[2]['classification'] == 'normal', \
        "Third classification should be 'normal'"
    
    assert received_classifications[1]['is_abnormal'] == True, \
        "Second classification should be abnormal"
    
    logger.info(f"\n✅ All assertions passed!")
    logger.info(f"=" * 80)
    logger.info(f"TEST PASSED: Classifications broadcast correctly at segment boundaries")
    logger.info(f"=" * 80)


async def test_producer_stats():
    """Test that producer stats include classification metrics."""
    
    logger.info("\n" + "=" * 80)
    logger.info("TEST: Producer Statistics")
    logger.info("=" * 80)
    
    # Create simple ECG source with classifications
    signal = np.sin(np.linspace(0, 10 * np.pi, 3600)).astype(np.float32)
    classifications = [
        {
            'segment_start_time': 0.0,
            'segment_duration': 10.0,
            'classification': 'normal',
            'confidence': 1.0,
            'is_abnormal': False,
            'annotation_source': 'TEST'
        }
    ]
    
    ecg_source = ECGSource(
        patient_id="test-patient",
        signal=signal,
        sample_rate=360,
        condition="test",
        patient_name="Test Patient",
        description="Test",
        classifications=classifications
    )
    
    producer = PatientProducer("test-patient", ecg_source, StreamConfig())
    await producer.start()
    
    # Wait a bit
    await asyncio.sleep(2.0)
    
    # Get stats
    stats = producer.get_stats()
    
    logger.info(f"\n📊 Producer Stats:")
    for key, value in stats.items():
        logger.info(f"   {key}: {value}")
    
    # Verify stats include classification fields
    assert 'classifications_sent' in stats, "Stats should include classifications_sent"
    assert 'total_classifications' in stats, "Stats should include total_classifications"
    assert stats['total_classifications'] == 1, "Should have 1 total classification"
    
    await producer.stop()
    
    logger.info(f"\n✅ Stats test passed!")
    logger.info(f"=" * 80)


async def main():
    """Run all tests."""
    try:
        await test_classification_broadcasting()
        await test_producer_stats()
        
        logger.info(f"\n🎉 ALL TESTS PASSED!")
        
    except AssertionError as e:
        logger.error(f"\n❌ TEST FAILED: {e}")
        raise
    except Exception as e:
        logger.error(f"\n❌ ERROR: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())

