import { useCallback } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "@tanstack/react-form";
import { toast } from "sonner";
import z from "zod";
import { authClient } from "@/lib/auth-client";
import { getErrorMessage } from "@/utils/error-handling";

export function useSignIn() {
  const router = useRouter();

  const form = useForm({
    defaultValues: {
      email: "",
      password: "",
    },
    onSubmit: async ({ value }) => {
      await authClient.signIn.email(
        {
          email: value.email,
          password: value.password,
        },
        {
          onSuccess: () => {
            router.push("/dashboard");
            toast.success("Sign in successful");
          },
          onError: (error) => {
            toast.error(getErrorMessage(error));
          },
        }
      );
    },
    validators: {
      onSubmit: z.object({
        email: z.email("Invalid email address"),
        password: z.string().min(8, "Password must be at least 8 characters"),
      }),
    },
  });

  const handleGoogleSignIn = useCallback(() => {
    // Google sign-in will be implemented when OAuth is added
    toast.info("Google sign-in coming soon");
  }, []);

  const handleForgotPassword = useCallback(() => {
    // Password reset will be implemented with doctor authentication
    toast.info("Forgot password functionality coming soon");
  }, []);

  return {
    form,
    handleGoogleSignIn,
    handleForgotPassword,
  };
}