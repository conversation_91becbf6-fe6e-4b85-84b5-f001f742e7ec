# ECG Simulator vs Real Data - Transition Guide

**Date**: October 8, 2025

---

## Summary

**You're absolutely correct!** The current system uses:
- ❌ **ECGSimulator** - Generates fake/synthetic ECG data
- ❌ **Hard-coded conditions** - MRN-based mapping (MRN001=normal, MRN002=afib, etc.)
- ❌ **Mathematical waveforms** - Not real patient data

**For production, we need:**
- ✅ **Real ECG recordings** from actual patients
- ✅ **ML-based classification** (not hard-coded)
- ✅ **Database storage** for patient recordings

---

## Why We Built It This Way (Development Phase)

The simulator was created for **development and testing** because:

1. **No real ECG data yet** - Database schema for recordings doesn't exist
2. **Testing the streaming architecture** - Needed data to test WebSocket, producers, etc.
3. **Demo purposes** - Show different cardiac conditions without real patient data
4. **Privacy/HIPAA** - Can't use real patient data during development

**But you're right - we don't need it for production!**

---

## What We Built (M1) is Still Correct

The **streaming architecture** we just implemented is **production-ready**:

✅ **PatientStreamRegistry** - Manages continuous producers  
✅ **PatientProducer** - Broadcasts chunks at real-time pace  
✅ **Subscribe API** - WebSocket clients subscribe to streams  
✅ **Real-time pacing** - 360 Hz with 0.25s chunks  

**We just need to swap the data source:**
- **Current**: `ECGSimulator` (fake data)
- **Production**: `ECGDataLoader` (real data)

---

## Transition Plan

### Phase 1: Add Database Schema (Priority 1)

Add ECG recording tables to `apps/server/src/db/schema/ecg.ts`:

```typescript
export const ecgRecordings = pgTable("ecg_recordings", {
  id: text("id").primaryKey(),
  patientId: text("patient_id").references(() => patients.id),
  recordedAt: timestamp("recorded_at").notNull(),
  durationSeconds: integer("duration_seconds").notNull(),
  sampleRate: integer("sample_rate").notNull().default(360),
  leadType: text("lead_type").notNull(), // 'I', 'II', 'III', etc.
  signalData: bytea("signal_data").notNull(), // Compressed binary
  metadata: jsonb("metadata"),
  createdAt: timestamp("created_at").default(sql`now()`),
});

export const ecgClassifications = pgTable("ecg_classifications", {
  id: text("id").primaryKey(),
  recordingId: text("recording_id").references(() => ecgRecordings.id),
  segmentStartTime: real("segment_start_time").notNull(),
  segmentDuration: real("segment_duration").notNull(),
  classification: text("classification").notNull(),
  confidence: real("confidence").notNull(),
  modelVersion: text("model_version").notNull(),
  classifiedAt: timestamp("classified_at").default(sql`now()`),
});
```

### Phase 2: Use ECGDataLoader (Priority 2)

Modify `PatientStreamRegistry` to use real data:

```python
# apps/ecg-server/src/ecg/streams.py

from .data_loader import ECGDataLoader  # NEW
from .simulator import ECGSimulator      # OLD (keep for fallback)

class PatientStreamRegistry:
    def __init__(
        self, 
        db_manager: DatabaseManager,
        config: Optional[StreamConfig] = None,
        seed: int = 42,
        use_real_data: bool = True  # NEW: Feature flag
    ):
        self.db_manager = db_manager
        self.config = config or StreamConfig()
        self.seed = seed
        self.use_real_data = use_real_data
        
        # Choose data source
        if use_real_data:
            self.ecg_source = ECGDataLoader(
                db_manager=db_manager,
                sample_rate=self.config.sample_rate
            )
        else:
            # Fallback to simulator for development
            self.ecg_source = ECGSimulator(
                sample_rate=self.config.sample_rate,
                db_manager=db_manager
            )
```

### Phase 3: Load Real Recordings

Update `_create_patient_ecg_mapping`:

```python
async def _create_patient_ecg_mapping(self, patient_ids: List[str]) -> None:
    """Load real ECG recordings for patients."""
    
    for patient_id in patient_ids:
        try:
            if self.use_real_data:
                # Load real recording from database
                recording = await self.ecg_source.load_patient_recording(patient_id)
                
                ecg_source = ECGSource(
                    patient_id=patient_id,
                    signal=recording.signal,
                    sample_rate=recording.sample_rate,
                    condition="unknown",  # Will be classified by ML model
                    patient_name=recording.patient_name,
                    description=f"Recording from {recording.recorded_at}"
                )
            else:
                # Use simulator (development/testing)
                patient_profile = await self.ecg_source._get_patient_profile(patient_id)
                signal = self.ecg_source._generate_patient_specific_ecg(patient_profile)
                
                ecg_source = ECGSource(
                    patient_id=patient_id,
                    signal=signal,
                    sample_rate=self.config.sample_rate,
                    condition=patient_profile.get("condition", "normal"),
                    patient_name=patient_profile.get("name", f"Patient {patient_id}"),
                    description=patient_profile.get("description", "")
                )
            
            self.patient_ecg_mapping[patient_id] = ecg_source
            
        except Exception as e:
            logger.error(f"❌ Error mapping patient {patient_id}: {e}")
```

### Phase 4: Environment Configuration

Add feature flag to `.env`:

```bash
# ECG Data Source
USE_REAL_ECG_DATA=true   # true = database, false = simulator
```

Update `main.py`:

```python
import os

# Initialize registry with real data if available
use_real_data = os.getenv("USE_REAL_ECG_DATA", "false").lower() == "true"

registry = PatientStreamRegistry(
    db_manager=db_manager,
    use_real_data=use_real_data
)
```

---

## What Happens to Hard-Coded Conditions?

### Current (Simulator)
```python
# Hard-coded in simulator.py
if mrn == "MRN001":
    condition = "normal"
elif mrn == "MRN002":
    condition = "atrial_fibrillation"
# etc...
```

### Production (ML Model)
```python
# Real-time classification
classifier = ECGClassifier(model_path="models/ecg_classifier.onnx")

# Classify each 10-second segment
classification = await classifier.classify_segment(signal_chunk)

# Store in database
await db.insert_classification(
    recording_id=recording_id,
    segment_start_time=segment_start,
    classification=classification.condition,
    confidence=classification.confidence
)
```

**No more hard-coding!** Conditions are detected by ML model.

---

## Sample Data for Testing

Until you have real patient recordings, use public ECG datasets:

### MIT-BIH Arrhythmia Database
```bash
# Download sample data
cd apps/ecg-server/datasets
wget -r -N -c -np https://physionet.org/files/mitdb/1.0.0/

# Load into database
poetry run python scripts/load_mitbih_data.py
```

### Create Seed Script
```python
# apps/ecg-server/scripts/load_sample_ecg.py

import wfdb
import gzip
import numpy as np
from src.database import DatabaseManager

async def load_mitbih_recordings():
    """Load MIT-BIH recordings into database."""
    
    db = DatabaseManager()
    await db.create_pool()
    
    # Load record 100 (normal sinus rhythm)
    record = wfdb.rdrecord('datasets/mitdb/100')
    signal = record.p_signal[:, 0]  # First lead (MLII)
    
    # Compress signal
    compressed = gzip.compress(signal.astype(np.float32).tobytes())
    
    # Insert into database
    await db.execute("""
        INSERT INTO ecg_recordings (
            id, patient_id, recorded_at, duration_seconds,
            sample_rate, lead_type, signal_data
        ) VALUES ($1, $2, NOW(), $3, $4, $5, $6)
    """, 
        "rec-100",
        "test-patient-1", 
        len(signal) / record.fs,
        record.fs,
        "MLII",
        compressed
    )
    
    print("✅ Loaded MIT-BIH record 100")
```

---

## Migration Checklist

- [ ] **Week 1**: Add ECG recording schema to database
- [ ] **Week 1**: Create migration scripts
- [ ] **Week 1**: Download MIT-BIH sample data
- [ ] **Week 2**: Implement `ECGDataLoader`
- [ ] **Week 2**: Add feature flag (`USE_REAL_ECG_DATA`)
- [ ] **Week 2**: Test with sample recordings
- [ ] **Week 3**: Integrate ML classifier
- [ ] **Week 3**: Remove hard-coded conditions
- [ ] **Week 4**: Production deployment with real data

---

## Files Created

1. **`docs/real-ecg-data-architecture.md`** - Complete architecture design
2. **`apps/ecg-server/src/ecg/data_loader.py`** - Real data loader implementation
3. **`docs/simulator-vs-real-data.md`** - This transition guide

---

## Key Takeaways

1. ✅ **Streaming architecture (M1) is correct** - No changes needed
2. ✅ **ECGDataLoader is ready** - Just needs database schema
3. ✅ **Feature flag allows gradual migration** - Can test both modes
4. ✅ **Simulator stays for development** - Useful for testing without real data
5. ✅ **ML model replaces hard-coding** - Real condition detection

**Next step**: Add ECG recording schema to database and start loading real data!

