"""
FastAPI ECG Streaming Server for HealthLink

This server provides real-time ECG data streaming capabilities
with authentication integration to the HealthLink system.
"""

import os
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAPI, Request, HTTPException, Depends, WebSocket, WebSocketDisconnect, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import HTT<PERSON><PERSON>earer
from dotenv import load_dotenv

from src.auth import SessionValidator, UserSession
from src.database import DatabaseManager
from src.models.schemas import HealthResponse
from src.ecg import ECGWebSocketManager

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv("LOG_LEVEL", "INFO")),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Initialize global components
db_manager = DatabaseManager()
session_validator = SessionValidator()
websocket_manager = ECGWebSocketManager()
security = HTTPBearer()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """FastAPI lifespan manager for startup and shutdown events."""
    # Startup
    logger.info("🚀 Starting ECG Server...")
    logger.info(f"📊 Server running on port {os.getenv('PORT', '8000')}")
    logger.info(f"🔗 Auth server URL: {os.getenv('AUTH_SERVER_URL', 'http://localhost:3000')}")
    
    # Initialize database connection pool (non-blocking)
    try:
        await db_manager.create_pool()
        logger.info("✅ Database connection pool initialized")
    except Exception as e:
        logger.warning(f"⚠️  Database connection failed during startup: {e}")
        logger.info("🔄 Server will continue without database - connections will be retried on demand")
    
    yield
    
    # Shutdown
    logger.info("🛑 Shutting down ECG Server...")
    await db_manager.close_pool()


# Initialize FastAPI app
app = FastAPI(
    title="HealthLink ECG Streaming Server",
    description="Real-time ECG signal streaming and classification service",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Configure CORS
CORS_ORIGINS = [
    origin.strip() 
    for origin in os.getenv("CORS_ORIGINS", "http://localhost:3001,http://localhost:3000").split(",")
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)


# Authentication dependency
async def get_current_user(token: str = Depends(security)) -> UserSession:
    """
    Validate session and return current user information.
    
    Args:
        token: Bearer token from Authorization header
        
    Returns:
        UserSession object with user information
        
    Raises:
        HTTPException: If token is invalid or user not authenticated
    """
    user = await session_validator.validate_session(token.credentials)
    if not user:
        raise HTTPException(
            status_code=401,
            detail="Invalid or expired session token"
        )
    return user


async def get_current_doctor(token: str = Depends(security)) -> UserSession:
    """
    Validate session and ensure user is a doctor or admin.
    
    Args:
        token: Bearer token from Authorization header
        
    Returns:
        UserSession object for doctor/admin user
        
    Raises:
        HTTPException: If token invalid or insufficient permissions
    """
    user = await session_validator.validate_doctor_role(token.credentials)
    if not user:
        raise HTTPException(
            status_code=403,
            detail="Insufficient permissions. Doctor or admin role required."
        )
    return user


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Global exception handler for unhandled errors."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error", "detail": "An unexpected error occurred"}
    )


# Health check endpoint
@app.get("/health", response_model=HealthResponse)
async def health_check() -> HealthResponse:
    """
    Health check endpoint for monitoring and load balancing.
    
    Returns:
        HealthResponse with server status and configuration info
    """
    # Check database connectivity
    db_healthy = await db_manager.health_check()
    
    # Check auth server connectivity  
    auth_healthy = await session_validator.health_check()
    
    # Determine overall status
    status = "healthy" if db_healthy and auth_healthy else "unhealthy"
    
    return HealthResponse(
        status=status,
        service="healthlink-ecg-server",
        version="0.1.0",
        environment={
            "port": int(os.getenv("PORT", "8000")),
            "debug": os.getenv("DEBUG", "false").lower() == "true",
            "auth_server": os.getenv("AUTH_SERVER_URL", "http://localhost:3000"),
            "database_healthy": db_healthy,
            "auth_server_healthy": auth_healthy,
        },
        endpoints={
            "health": "/health",
            "docs": "/docs",
            "websocket": "/ws/ecg/{patient_id}",
        }
    )


# Root endpoint
@app.get("/")
async def root() -> Dict[str, str]:
    """Root endpoint with basic service information."""
    return {
        "message": "HealthLink ECG Streaming Server",
        "documentation": "/docs",
        "health": "/health"
    }


# Protected endpoint for testing authentication
@app.get("/patients")
async def get_doctor_patients(current_user: UserSession = Depends(get_current_doctor)) -> Dict[str, Any]:
    """
    Get patients assigned to the authenticated doctor.
    
    Args:
        current_user: Authenticated doctor user session
        
    Returns:
        List of assigned patients
    """
    patients = await db_manager.get_doctor_patients(current_user.user_id)
    
    return {
        "doctor": {
            "user_id": current_user.user_id,
            "name": current_user.name,
            "email": current_user.email,
            "role": current_user.role
        },
        "patients": patients,
        "count": len(patients)
    }


# Patient access verification endpoint
@app.get("/patients/{patient_id}")
async def get_patient_info(
    patient_id: str,
    current_user: UserSession = Depends(get_current_doctor)
) -> Dict[str, Any]:
    """
    Get patient information if doctor has access.
    
    Args:
        patient_id: Patient ID to retrieve
        current_user: Authenticated doctor user session
        
    Returns:
        Patient information
        
    Raises:
        HTTPException: If patient not found or access denied
    """
    # Verify doctor has access to this patient
    has_access = await db_manager.verify_patient_access(current_user.user_id, patient_id)
    
    if not has_access:
        raise HTTPException(
            status_code=403,
            detail="Access denied. Patient not assigned to this doctor."
        )
    
    # Get patient information
    patient_info = await db_manager.get_patient_info(patient_id)
    
    if not patient_info:
        raise HTTPException(
            status_code=404,
            detail="Patient not found"
        )
    
    return {
        "patient": patient_info,
        "access_granted": True,
        "doctor_id": current_user.user_id
    }


# Debug endpoint for authentication testing
@app.get("/debug/auth/{token}")
async def debug_auth(token: str) -> Dict[str, Any]:
    """
    Debug endpoint to test authentication validation.

    Args:
        token: Session token or user ID to validate

    Returns:
        Authentication validation results
    """
    try:
        logger.info(f"🔍 Debug auth validation for token: {token[:10]}...")

        # Test session validation
        user = await session_validator.validate_session(token)

        if user:
            return {
                "success": True,
                "user": {
                    "id": user.user_id,
                    "email": user.email,
                    "name": user.name,
                    "role": user.role,
                    "account_status": user.account_status
                },
                "message": "Authentication successful"
            }
        else:
            return {
                "success": False,
                "message": "Authentication failed",
                "token_preview": f"{token[:10]}..."
            }

    except Exception as e:
        logger.error(f"❌ Debug auth error: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "Authentication validation error"
        }


# WebSocket endpoint for ECG streaming
@app.websocket("/ws/ecg/{patient_id}")
async def websocket_endpoint(websocket: WebSocket, patient_id: str, token: str = Query(...)):
    """
    WebSocket endpoint for real-time ECG streaming.

    Args:
        websocket: WebSocket connection
        patient_id: Patient ID to stream ECG data for
        token: Authentication token (passed as query parameter)
    """
    try:
        logger.info(f"🔗 WebSocket connection attempt for patient {patient_id} with token {token[:10]}...")

        # Validate session
        user = await session_validator.validate_session(token)
        if not user:
            logger.error(f"❌ Authentication failed for token {token[:10]}...")
            await websocket.close(code=1008, reason="Authentication failed")
            return

        logger.info(f"✅ User authenticated: {user.email} (role: {user.role}, id: {user.user_id})")

        # Check if user has doctor or admin role
        if user.role not in ["doctor", "admin"]:
            logger.error(f"❌ Insufficient permissions for user {user.email} (role: {user.role})")
            await websocket.close(code=1008, reason="Insufficient permissions")
            return

        logger.info(f"✅ Role check passed for user {user.email}")

        # Check if doctor has access to this patient
        logger.info(f"🔍 Checking patient access for doctor {user.user_id} -> patient {patient_id}")
        has_access = await websocket_manager.verify_patient_access(user.user_id, patient_id)
        if not has_access:
            logger.error(f"❌ Access denied: Doctor {user.user_id} cannot access patient {patient_id}")
            await websocket.close(code=1008, reason="Access denied")
            return

        logger.info(f"✅ Patient access granted for doctor {user.user_id} -> patient {patient_id}")

        # Accept connection and start streaming
        await websocket.accept()
        logger.info(f"🎉 WebSocket connection accepted for patient {patient_id}")
        await websocket_manager.handle_connection(websocket, patient_id, user.user_id)

    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for patient {patient_id}")
    except Exception as e:
        logger.error(f"WebSocket error for patient {patient_id}: {e}")
        try:
            await websocket.close(code=1011, reason="Internal server error")
        except:
            pass


if __name__ == "__main__":
    import uvicorn
    
    # Development server configuration
    uvicorn.run(
        "main:app",
        host=os.getenv("HOST", "0.0.0.0"),
        port=int(os.getenv("PORT", "8000")),
        reload=os.getenv("DEBUG", "false").lower() == "true",
        log_level=os.getenv("LOG_LEVEL", "info").lower()
    )