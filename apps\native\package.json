{"name": "native", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"dev": "expo start --clear", "android": "expo run:android", "ios": "expo run:ios", "prebuild": "expo prebuild", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.0.4", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/drawer": "^7.1.1", "@react-navigation/native": "^7.0.14", "@tanstack/react-form": "^1.0.5", "@tanstack/react-query": "^5.80.5", "expo": "^53.0.4", "expo-constants": "~17.1.4", "expo-linking": "~7.1.4", "expo-navigation-bar": "~4.2.3", "expo-router": "~5.0.3", "expo-secure-store": "~14.2.3", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.6", "expo-web-browser": "~14.1.6", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.1", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.3.0", "react-native-screens": "~4.10.0", "react-native-web": "^0.20.0", "@trpc/tanstack-react-query": "^11.4.2", "@trpc/client": "^11.4.2", "@trpc/server": "^11.4.2", "better-auth": "^1.3.4", "@better-auth/expo": "^1.3.4"}, "devDependencies": {"@babel/core": "^7.26.10", "@types/react": "~19.0.10", "tailwindcss": "^3.4.17", "typescript": "~5.8.2"}, "private": true}