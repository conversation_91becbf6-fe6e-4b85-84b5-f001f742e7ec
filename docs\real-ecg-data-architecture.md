# Real ECG Data Architecture - Production System

**Date**: October 8, 2025  
**Status**: Design Document

---

## Problem Statement

The current implementation uses **synthetic/simulated ECG data** with hard-coded conditions. For a production Clinical Decision Support System, we need to:

1. Store and stream **real ECG recordings** from actual patients
2. Use **ML models** to detect conditions (not hard-coded MRN mappings)
3. Support **live ECG device streaming** and **historical playback**
4. Maintain **patient privacy** and **HIPAA compliance**

---

## Current vs. Proposed Architecture

### Current (Development/Testing)
```
Patient Request
    ↓
ECGSimulator (generates fake data)
    ↓
Hard-coded condition based on MRN
    ↓
Mathematical ECG waveform generation
    ↓
Stream to client
```

**Problems:**
- ❌ No real ECG data
- ❌ Hard-coded conditions (MRN001 = normal, MRN002 = afib, etc.)
- ❌ Synthetic waveforms don't match real patient conditions
- ❌ Can't replay historical recordings
- ❌ No ML model integration

### Proposed (Production)
```
Patient Request
    ↓
Database: Fetch patient's ECG recordings
    ↓
ECG Data Source (file/blob storage/database)
    ↓
ML Model: Classify ECG segments
    ↓
Stream real data + classifications to client
```

**Benefits:**
- ✅ Real patient ECG data
- ✅ ML-based condition detection
- ✅ Historical playback capability
- ✅ Live device integration ready
- ✅ Accurate clinical data

---

## Data Storage Options

### Option 1: Database Storage (Recommended for MVP)

**Schema:**
```sql
CREATE TABLE ecg_recordings (
    id UUID PRIMARY KEY,
    patient_id TEXT REFERENCES patients(id),
    recorded_at TIMESTAMP NOT NULL,
    duration_seconds INTEGER NOT NULL,
    sample_rate INTEGER NOT NULL DEFAULT 360,
    lead_type TEXT NOT NULL, -- 'I', 'II', 'III', 'aVR', 'aVL', 'aVF', 'V1-V6'
    signal_data BYTEA NOT NULL, -- Binary ECG data (compressed)
    metadata JSONB, -- Device info, quality metrics, etc.
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE ecg_classifications (
    id UUID PRIMARY KEY,
    recording_id UUID REFERENCES ecg_recordings(id),
    segment_start_time FLOAT NOT NULL, -- Seconds from recording start
    segment_duration FLOAT NOT NULL,
    classification TEXT NOT NULL, -- 'normal', 'afib', 'vtach', etc.
    confidence FLOAT NOT NULL, -- 0.0 to 1.0
    model_version TEXT NOT NULL,
    classified_at TIMESTAMP DEFAULT NOW()
);
```

**Pros:**
- Easy to query and filter
- ACID compliance
- Built-in backup/replication
- Good for segments up to ~1 hour

**Cons:**
- Large recordings can bloat database
- May need compression

### Option 2: Blob Storage (S3/MinIO) + Database Metadata

**Schema:**
```sql
CREATE TABLE ecg_recordings (
    id UUID PRIMARY KEY,
    patient_id TEXT REFERENCES patients(id),
    recorded_at TIMESTAMP NOT NULL,
    duration_seconds INTEGER NOT NULL,
    sample_rate INTEGER NOT NULL DEFAULT 360,
    storage_path TEXT NOT NULL, -- s3://bucket/patient-id/recording-id.dat
    format TEXT NOT NULL, -- 'wfdb', 'edf', 'raw_float32'
    metadata JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);
```

**Pros:**
- Scalable for large recordings (hours/days)
- Cost-effective storage
- Can use specialized formats (WFDB, EDF)

**Cons:**
- More complex infrastructure
- Network latency for retrieval

### Option 3: Hybrid (Recommended for Production)

- **Recent/Active recordings** (last 24 hours): Database
- **Historical recordings**: Blob storage
- **Metadata**: Always in database

---

## ECG Data Formats

### 1. Raw Binary (Float32 Array)
```python
# 10 seconds at 360 Hz = 3600 samples
signal = np.array([...], dtype=np.float32)  # 14.4 KB
compressed = gzip.compress(signal.tobytes())  # ~5-8 KB
```

### 2. WFDB Format (MIT-BIH Standard)
```python
import wfdb
record = wfdb.rdrecord('patient-123/recording-456')
signal = record.p_signal[:, 0]  # First lead
```

### 3. EDF Format (European Data Format)
```python
import pyedflib
f = pyedflib.EdfReader('recording.edf')
signal = f.readSignal(0)  # First channel
```

**Recommendation**: Start with **compressed Float32 binary** for simplicity, migrate to WFDB for compatibility.

---

## Implementation Plan

### Phase 1: Database Schema (Week 1)

1. **Add ECG recording tables** to `apps/server/src/db/schema/ecg.ts`
2. **Migration scripts** to create tables
3. **Seed script** to load sample ECG data (MIT-BIH or PhysioNet)

### Phase 2: ECG Data Loader (Week 1-2)

Replace `ECGSimulator` with `ECGDataLoader`:

```python
class ECGDataLoader:
    """Loads real ECG data from database or storage."""
    
    async def load_patient_recording(
        self, 
        patient_id: str,
        recording_id: Optional[str] = None
    ) -> ECGRecording:
        """
        Load ECG recording for patient.
        
        Args:
            patient_id: Patient identifier
            recording_id: Specific recording (None = most recent)
            
        Returns:
            ECGRecording with signal data and metadata
        """
        if recording_id is None:
            # Get most recent recording
            recording_id = await self._get_latest_recording_id(patient_id)
        
        # Fetch from database
        recording_data = await self.db.fetch_recording(recording_id)
        
        # Decompress signal
        signal = self._decompress_signal(recording_data['signal_data'])
        
        return ECGRecording(
            id=recording_id,
            patient_id=patient_id,
            signal=signal,
            sample_rate=recording_data['sample_rate'],
            recorded_at=recording_data['recorded_at'],
            metadata=recording_data['metadata']
        )
```

### Phase 3: ML Model Integration (Week 2-3)

Replace hard-coded conditions with ML predictions:

```python
class ECGClassifier:
    """Real ML-based ECG classifier."""
    
    def __init__(self, model_path: str):
        # Load trained model (TensorFlow, PyTorch, ONNX)
        self.model = load_model(model_path)
    
    async def classify_segment(
        self, 
        signal: np.ndarray,
        sample_rate: int = 360
    ) -> Classification:
        """
        Classify ECG segment using ML model.
        
        Returns:
            Classification with condition and confidence
        """
        # Preprocess
        preprocessed = self._preprocess(signal, sample_rate)
        
        # Predict
        predictions = self.model.predict(preprocessed)
        
        # Get top prediction
        condition = self.classes[np.argmax(predictions)]
        confidence = float(np.max(predictions))
        
        return Classification(
            condition=condition,
            confidence=confidence,
            all_predictions=dict(zip(self.classes, predictions))
        )
```

### Phase 4: Update Streaming Architecture (Week 3)

Modify `PatientStreamRegistry` to use real data:

```python
async def _create_patient_ecg_mapping(self, patient_ids: List[str]) -> None:
    """Load real ECG recordings for patients."""
    
    for patient_id in patient_ids:
        # Load real recording from database
        recording = await self.ecg_loader.load_patient_recording(patient_id)
        
        # Create ECG source with real data
        ecg_source = ECGSource(
            patient_id=patient_id,
            signal=recording.signal,
            sample_rate=recording.sample_rate,
            condition="unknown",  # Will be classified in real-time
            patient_name=recording.patient_name,
            description=f"Recording from {recording.recorded_at}"
        )
        
        self.patient_ecg_mapping[patient_id] = ecg_source
```

---

## Migration Strategy

### Step 1: Keep Simulator for Development
- Keep `ECGSimulator` for testing/development
- Add feature flag: `USE_REAL_ECG_DATA=false` (default)

### Step 2: Implement Parallel System
- Create `ECGDataLoader` alongside `ECGSimulator`
- Use environment variable to switch between them

### Step 3: Gradual Migration
```python
class PatientStreamRegistry:
    def __init__(self, db_manager, use_real_data=False):
        if use_real_data:
            self.ecg_source = ECGDataLoader(db_manager)
        else:
            self.ecg_source = ECGSimulator(db_manager)
```

### Step 4: Full Production
- Remove `ECGSimulator`
- Use only `ECGDataLoader`
- ML model for all classifications

---

## Sample Data Sources

For testing with real ECG data:

1. **MIT-BIH Arrhythmia Database**
   - URL: https://physionet.org/content/mitdb/1.0.0/
   - 48 half-hour recordings
   - Annotated arrhythmias
   - Free for research

2. **PhysioNet Databases**
   - URL: https://physionet.org/
   - Multiple ECG datasets
   - Various conditions
   - Well-documented

3. **PTB Diagnostic ECG Database**
   - URL: https://physionet.org/content/ptbdb/1.0.0/
   - 549 records from 290 subjects
   - Healthy and pathological

---

## Next Steps

1. **Immediate (This Week)**:
   - Add ECG recording schema to database
   - Download MIT-BIH sample data
   - Create seed script to load sample recordings

2. **Short-term (Next 2 Weeks)**:
   - Implement `ECGDataLoader`
   - Add feature flag to switch between simulator and real data
   - Test streaming with real recordings

3. **Medium-term (Next Month)**:
   - Integrate ML model for classification
   - Remove hard-coded condition mappings
   - Production-ready data pipeline

4. **Long-term (Next Quarter)**:
   - Live ECG device integration
   - Real-time classification
   - Historical playback UI

---

## Conclusion

**You're absolutely right** - we don't need the hard-coded simulator for production. The current implementation is just for **development/testing** until we have:

1. Real ECG recordings in the database
2. ML models for classification
3. Live device integration

The streaming architecture (M1) we just built is **correct and production-ready** - we just need to swap the data source from `ECGSimulator` (fake) to `ECGDataLoader` (real).

