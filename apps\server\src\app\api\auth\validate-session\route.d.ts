import { NextRequest, NextResponse } from "next/server";
/**
 * Custom endpoint for external services (like ECG server) to validate sessions
 * Uses the recommended Better Auth auth.api.getSession() approach with automatic cookie handling
 */
export declare function POST(request: NextRequest): Promise<NextResponse<{
    error: string;
}> | NextResponse<{
    user: {
        id: string;
        name: string;
        email: string;
        role: string | null | undefined;
        accountStatus: string | null | undefined;
    };
    session: {
        id: string;
        userId: string;
        expiresAt: Date;
    };
}>>;
export declare function GET(request: NextRequest): Promise<NextResponse<{
    error: string;
}> | NextResponse<{
    user: {
        id: string;
        name: string;
        email: string;
        role: string | null | undefined;
        accountStatus: string | null | undefined;
    };
    session: {
        id: string;
        userId: string;
        expiresAt: Date;
    };
}>>;
