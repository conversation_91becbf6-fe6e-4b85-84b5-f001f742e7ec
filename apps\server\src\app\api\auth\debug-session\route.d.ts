import { NextRequest, NextResponse } from "next/server";
/**
 * Debug endpoint to understand how Better Auth sessions work
 */
export declare function GET(request: NextRequest): Promise<NextResponse<{
    hasSession: boolean;
    user: {
        id: string;
        email: string;
        name: string;
        role: string | null | undefined;
    } | null;
    sessionId: string | undefined;
    cookies: {
        [k: string]: string;
    };
}> | NextResponse<{
    error: any;
    hasSession: boolean;
}>>;
export declare function POST(request: NextRequest): Promise<NextResponse<{
    token: string;
    results: {};
}> | NextResponse<{
    error: any;
}>>;
