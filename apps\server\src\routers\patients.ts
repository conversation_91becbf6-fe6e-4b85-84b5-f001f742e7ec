import { z } from "zod";
import { eq, and } from "drizzle-orm";
import { protectedProcedure, router } from "../lib/trpc";
import { db } from "../db";
import { patients } from "../db/schema/ecg";
import { doctors } from "../db/schema/doctors";
import { user } from "../db/schema/auth";
import { TRPCError } from "@trpc/server";

/**
 * Patient Router
 * 
 * Handles patient-related operations with proper authentication and authorization.
 * Only doctors can access patient data for patients assigned to them.
 */
export const patientRouter = router({
  /**
   * Get patients assigned to the authenticated doctor
   */
  getAssignedPatients: protectedProcedure.query(async ({ ctx }) => {
    const { session } = ctx;
    
    // Ensure user is a doctor
    if (session.user.role !== "doctor") {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "Only doctors can access patient data",
      });
    }

    try {
      // First, get the doctor record for the authenticated user
      const doctorRecord = await db
        .select()
        .from(doctors)
        .where(eq(doctors.userId, session.user.id))
        .limit(1);

      if (doctorRecord.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Doctor profile not found",
        });
      }

      const doctorId = doctorRecord[0].id;

      // Get all patients assigned to this doctor
      const assignedPatients = await db
        .select({
          id: patients.id,
          medicalRecordNumber: patients.medicalRecordNumber,
          dateOfBirth: patients.dateOfBirth,
          gender: patients.gender,
          emergencyContact: patients.emergencyContact,
          emergencyPhone: patients.emergencyPhone,
          createdAt: patients.createdAt,
          // Get patient user info
          name: user.name,
          email: user.email,
        })
        .from(patients)
        .innerJoin(user, eq(patients.userId, user.id))
        .where(eq(patients.assignedDoctorId, doctorId));

      return {
        patients: assignedPatients,
        count: assignedPatients.length,
        doctorId,
      };
    } catch (error) {
      console.error("Error fetching assigned patients:", error);
      
      if (error instanceof TRPCError) {
        throw error;
      }
      
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch assigned patients",
      });
    }
  }),

  /**
   * Get specific patient information (if doctor has access)
   */
  getPatientById: protectedProcedure
    .input(z.object({ patientId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { session } = ctx;
      const { patientId } = input;

      // Ensure user is a doctor
      if (session.user.role !== "doctor") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Only doctors can access patient data",
        });
      }

      try {
        // Get the doctor record
        const doctorRecord = await db
          .select()
          .from(doctors)
          .where(eq(doctors.userId, session.user.id))
          .limit(1);

        if (doctorRecord.length === 0) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Doctor profile not found",
          });
        }

        const doctorId = doctorRecord[0].id;

        // Get patient info if assigned to this doctor
        const patientInfo = await db
          .select({
            id: patients.id,
            medicalRecordNumber: patients.medicalRecordNumber,
            dateOfBirth: patients.dateOfBirth,
            gender: patients.gender,
            emergencyContact: patients.emergencyContact,
            emergencyPhone: patients.emergencyPhone,
            assignedDoctorId: patients.assignedDoctorId,
            createdAt: patients.createdAt,
            updatedAt: patients.updatedAt,
            // Get patient user info
            name: user.name,
            email: user.email,
            userId: user.id,
          })
          .from(patients)
          .innerJoin(user, eq(patients.userId, user.id))
          .where(
            and(
              eq(patients.id, patientId),
              eq(patients.assignedDoctorId, doctorId)
            )
          )
          .limit(1);

        if (patientInfo.length === 0) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Patient not found or not assigned to this doctor",
          });
        }

        return {
          patient: patientInfo[0],
          accessGranted: true,
          doctorId,
        };
      } catch (error) {
        console.error("Error fetching patient by ID:", error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch patient information",
        });
      }
    }),

  /**
   * Verify if doctor has access to a specific patient
   */
  verifyPatientAccess: protectedProcedure
    .input(z.object({ patientId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { session } = ctx;
      const { patientId } = input;

      // Ensure user is a doctor
      if (session.user.role !== "doctor") {
        return { hasAccess: false, reason: "Not a doctor" };
      }

      try {
        // Get the doctor record
        const doctorRecord = await db
          .select()
          .from(doctors)
          .where(eq(doctors.userId, session.user.id))
          .limit(1);

        if (doctorRecord.length === 0) {
          return { hasAccess: false, reason: "Doctor profile not found" };
        }

        const doctorId = doctorRecord[0].id;

        // Check if patient is assigned to this doctor
        const patientAssignment = await db
          .select({ id: patients.id })
          .from(patients)
          .where(
            and(
              eq(patients.id, patientId),
              eq(patients.assignedDoctorId, doctorId)
            )
          )
          .limit(1);

        return {
          hasAccess: patientAssignment.length > 0,
          reason: patientAssignment.length > 0 ? "Access granted" : "Patient not assigned to doctor",
          doctorId,
        };
      } catch (error) {
        console.error("Error verifying patient access:", error);
        return { hasAccess: false, reason: "Database error" };
      }
    }),
});
