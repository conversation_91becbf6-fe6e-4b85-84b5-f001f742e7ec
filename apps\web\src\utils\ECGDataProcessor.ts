/**
 * ECG Data Processing Utilities
 * 
 * Inspired by React-Electrocardiogram's efficient data handling approach.
 * Optimized for real-time ECG data processing with medical-grade precision.
 */

import type { ECGDataPoint, ClassificationResult } from "@/types/ecg";

export interface ProcessedECGData {
  points: Array<{ x: number; y: number }>;
  statistics: ECGStatistics;
  qualityMetrics: DataQualityMetrics;
}

export interface ECGStatistics {
  heartRate: number;
  rrInterval: number;
  amplitude: {
    min: number;
    max: number;
    mean: number;
    std: number;
  };
  signalQuality: number; // 0-1 scale
}

export interface DataQualityMetrics {
  samplingRate: number;
  droppedSamples: number;
  noiseLevel: number;
  baselineDrift: number;
  signalToNoise: number;
}

export interface ECGBuffer {
  data: ECGDataPoint[];
  maxSize: number;
  currentIndex: number;
  isCircular: boolean;
}

/**
 * High-performance circular buffer for ECG data
 * Inspired by React-ECG's memory-efficient approach
 */
export class CircularECGBuffer {
  private buffer: ECGDataPoint[];
  private head: number = 0;
  private tail: number = 0;
  private size: number = 0;
  private readonly capacity: number;

  constructor(capacity: number = 50000) {
    this.capacity = capacity;
    this.buffer = new Array(capacity);
  }

  /**
   * Add new ECG data point(s) to the buffer
   */
  add(point: ECGDataPoint | ECGDataPoint[]): void {
    if (Array.isArray(point)) {
      point.forEach(p => this.addSingle(p));
    } else {
      this.addSingle(point);
    }
  }

  private addSingle(point: ECGDataPoint): void {
    this.buffer[this.head] = point;
    this.head = (this.head + 1) % this.capacity;
    
    if (this.size < this.capacity) {
      this.size++;
    } else {
      // Buffer is full, move tail
      this.tail = (this.tail + 1) % this.capacity;
    }
  }

  /**
   * Get the latest N data points
   */
  getLatest(count: number): ECGDataPoint[] {
    const result: ECGDataPoint[] = [];
    const actualCount = Math.min(count, this.size);
    
    for (let i = 0; i < actualCount; i++) {
      const index = (this.head - 1 - i + this.capacity) % this.capacity;
      result.unshift(this.buffer[index]);
    }
    
    return result;
  }

  /**
   * Get data points within a time range
   */
  getTimeRange(startTime: number, endTime: number): ECGDataPoint[] {
    const result: ECGDataPoint[] = [];
    
    for (let i = 0; i < this.size; i++) {
      const index = (this.tail + i) % this.capacity;
      const point = this.buffer[index];
      
      if (point.timestamp >= startTime && point.timestamp <= endTime) {
        result.push(point);
      }
    }
    
    return result;
  }

  /**
   * Get all data points in chronological order
   */
  getAll(): ECGDataPoint[] {
    const result: ECGDataPoint[] = [];
    
    for (let i = 0; i < this.size; i++) {
      const index = (this.tail + i) % this.capacity;
      result.push(this.buffer[index]);
    }
    
    return result;
  }

  /**
   * Clear the buffer
   */
  clear(): void {
    this.head = 0;
    this.tail = 0;
    this.size = 0;
  }

  /**
   * Get buffer statistics
   */
  getStats(): { size: number; capacity: number; utilization: number } {
    return {
      size: this.size,
      capacity: this.capacity,
      utilization: this.size / this.capacity,
    };
  }
}

/**
 * ECG Data Processor
 * Handles real-time ECG data processing with medical-grade algorithms
 */
export class ECGDataProcessor {
  private buffer: CircularECGBuffer;
  private sampleRate: number;
  private lastHeartRateCalculation: number = 0;
  private rrIntervals: number[] = [];

  constructor(sampleRate: number = 360, bufferSize: number = 50000) {
    this.buffer = new CircularECGBuffer(bufferSize);
    this.sampleRate = sampleRate;
  }

  /**
   * Process new ECG data points
   */
  processData(newPoints: ECGDataPoint[]): ProcessedECGData {
    // Add to buffer
    this.buffer.add(newPoints);

    // Get recent data for processing
    const recentData = this.buffer.getLatest(this.sampleRate * 10); // Last 10 seconds

    // Convert to chart format (React-ECG approach)
    const chartPoints = recentData.map(point => ({
      x: point.timestamp,
      y: point.value,
    }));

    // Calculate statistics
    const statistics = this.calculateStatistics(recentData);
    
    // Calculate quality metrics
    const qualityMetrics = this.calculateQualityMetrics(recentData);

    return {
      points: chartPoints,
      statistics,
      qualityMetrics,
    };
  }

  /**
   * Calculate ECG statistics (heart rate, amplitude, etc.)
   */
  private calculateStatistics(data: ECGDataPoint[]): ECGStatistics {
    if (data.length === 0) {
      return {
        heartRate: 0,
        rrInterval: 0,
        amplitude: { min: 0, max: 0, mean: 0, std: 0 },
        signalQuality: 0,
      };
    }

    // Calculate amplitude statistics
    const values = data.map(p => p.value);
    const min = Math.min(...values);
    const max = Math.max(...values);
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    const std = Math.sqrt(variance);

    // Calculate heart rate (simplified R-peak detection)
    const heartRate = this.calculateHeartRate(data);
    const rrInterval = heartRate > 0 ? 60000 / heartRate : 0; // ms

    // Calculate signal quality (simplified)
    const signalQuality = this.calculateSignalQuality(data);

    return {
      heartRate,
      rrInterval,
      amplitude: { min, max, mean, std },
      signalQuality,
    };
  }

  /**
   * Simplified heart rate calculation using peak detection
   */
  private calculateHeartRate(data: ECGDataPoint[]): number {
    if (data.length < this.sampleRate * 2) return 0; // Need at least 2 seconds

    // Simple peak detection (threshold-based)
    const threshold = this.calculateAdaptiveThreshold(data);
    const peaks: number[] = [];

    for (let i = 1; i < data.length - 1; i++) {
      const current = data[i].value;
      const prev = data[i - 1].value;
      const next = data[i + 1].value;

      // Local maximum above threshold
      if (current > prev && current > next && current > threshold) {
        // Ensure minimum distance between peaks (300ms = 200 BPM max)
        const lastPeak = peaks[peaks.length - 1];
        if (!lastPeak || (data[i].timestamp - lastPeak) > 300) {
          peaks.push(data[i].timestamp);
        }
      }
    }

    // Calculate heart rate from R-R intervals
    if (peaks.length < 2) return 0;

    const intervals = [];
    for (let i = 1; i < peaks.length; i++) {
      intervals.push(peaks[i] - peaks[i - 1]);
    }

    const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
    return Math.round(60000 / avgInterval); // Convert to BPM
  }

  /**
   * Calculate adaptive threshold for peak detection
   */
  private calculateAdaptiveThreshold(data: ECGDataPoint[]): number {
    const values = data.map(p => p.value);
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const max = Math.max(...values);
    
    // Threshold is 60% between mean and max
    return mean + (max - mean) * 0.6;
  }

  /**
   * Calculate data quality metrics
   */
  private calculateQualityMetrics(data: ECGDataPoint[]): DataQualityMetrics {
    if (data.length < 2) {
      return {
        samplingRate: 0,
        droppedSamples: 0,
        noiseLevel: 0,
        baselineDrift: 0,
        signalToNoise: 0,
      };
    }

    // Calculate actual sampling rate
    const timeSpan = data[data.length - 1].timestamp - data[0].timestamp;
    const actualSampleRate = (data.length - 1) / (timeSpan / 1000);

    // Detect dropped samples
    const expectedSamples = Math.floor(timeSpan / (1000 / this.sampleRate));
    const droppedSamples = Math.max(0, expectedSamples - data.length);

    // Calculate noise level (high-frequency component)
    const noiseLevel = this.calculateNoiseLevel(data);

    // Calculate baseline drift (low-frequency component)
    const baselineDrift = this.calculateBaselineDrift(data);

    // Signal-to-noise ratio
    const signalPower = this.calculateSignalPower(data);
    const noisePower = Math.pow(noiseLevel, 2);
    const signalToNoise = noisePower > 0 ? 10 * Math.log10(signalPower / noisePower) : 0;

    return {
      samplingRate: actualSampleRate,
      droppedSamples,
      noiseLevel,
      baselineDrift,
      signalToNoise,
    };
  }

  /**
   * Calculate noise level using high-pass filtering approximation
   */
  private calculateNoiseLevel(data: ECGDataPoint[]): number {
    if (data.length < 3) return 0;

    // Simple high-pass filter approximation (difference between adjacent samples)
    const differences = [];
    for (let i = 1; i < data.length; i++) {
      differences.push(Math.abs(data[i].value - data[i - 1].value));
    }

    return differences.reduce((sum, diff) => sum + diff, 0) / differences.length;
  }

  /**
   * Calculate baseline drift using moving average
   */
  private calculateBaselineDrift(data: ECGDataPoint[]): number {
    if (data.length < 10) return 0;

    const windowSize = Math.min(this.sampleRate, data.length); // 1 second window
    const baseline = [];

    for (let i = windowSize; i < data.length; i++) {
      const window = data.slice(i - windowSize, i);
      const avg = window.reduce((sum, p) => sum + p.value, 0) / window.length;
      baseline.push(avg);
    }

    if (baseline.length < 2) return 0;

    // Calculate drift as difference between first and last baseline values
    return Math.abs(baseline[baseline.length - 1] - baseline[0]);
  }

  /**
   * Calculate signal power
   */
  private calculateSignalPower(data: ECGDataPoint[]): number {
    const values = data.map(p => p.value);
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    
    return values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
  }

  /**
   * Calculate signal quality score (0-1)
   */
  private calculateSignalQuality(data: ECGDataPoint[]): number {
    const metrics = this.calculateQualityMetrics(data);
    
    // Quality factors (0-1 scale)
    const samplingQuality = Math.min(1, metrics.samplingRate / this.sampleRate);
    const droppedSampleQuality = Math.max(0, 1 - (metrics.droppedSamples / data.length));
    const snrQuality = Math.min(1, Math.max(0, metrics.signalToNoise / 20)); // 20dB = good quality
    
    // Weighted average
    return (samplingQuality * 0.3 + droppedSampleQuality * 0.3 + snrQuality * 0.4);
  }

  /**
   * Get buffer statistics
   */
  getBufferStats() {
    return this.buffer.getStats();
  }

  /**
   * Clear all data
   */
  clear(): void {
    this.buffer.clear();
    this.rrIntervals = [];
    this.lastHeartRateCalculation = 0;
  }
}

/**
 * Utility functions for ECG data conversion
 */
export const ECGUtils = {
  /**
   * Convert HealthLink ECG format to LightningChart format
   */
  convertToChartFormat(data: ECGDataPoint[]): Array<{ x: number; y: number }> {
    return data.map(point => ({
      x: point.timestamp,
      y: point.value,
    }));
  },

  /**
   * Convert React-ECG format to HealthLink format
   */
  convertFromReactECG(data: Array<{ x: number; y: number }>): ECGDataPoint[] {
    return data.map(point => ({
      timestamp: point.x,
      value: point.y,
    }));
  },

  /**
   * Resample ECG data to target sample rate
   */
  resample(data: ECGDataPoint[], targetSampleRate: number): ECGDataPoint[] {
    if (data.length < 2) return data;

    const timeSpan = data[data.length - 1].timestamp - data[0].timestamp;
    const currentSampleRate = (data.length - 1) / (timeSpan / 1000);
    
    if (Math.abs(currentSampleRate - targetSampleRate) < 1) {
      return data; // Already at target rate
    }

    const ratio = targetSampleRate / currentSampleRate;
    const result: ECGDataPoint[] = [];

    for (let i = 0; i < data.length - 1; i += 1 / ratio) {
      const index = Math.floor(i);
      const fraction = i - index;
      
      if (index + 1 < data.length) {
        // Linear interpolation
        const value = data[index].value + fraction * (data[index + 1].value - data[index].value);
        const timestamp = data[index].timestamp + fraction * (data[index + 1].timestamp - data[index].timestamp);
        
        result.push({ timestamp, value });
      }
    }

    return result;
  },

  /**
   * Apply basic ECG filtering
   */
  applyBasicFilter(data: ECGDataPoint[], sampleRate: number = 360): ECGDataPoint[] {
    if (data.length < 5) return data;

    // Simple moving average filter (3-point)
    const filtered: ECGDataPoint[] = [data[0]]; // Keep first point

    for (let i = 1; i < data.length - 1; i++) {
      const smoothedValue = (data[i - 1].value + data[i].value + data[i + 1].value) / 3;
      filtered.push({
        timestamp: data[i].timestamp,
        value: smoothedValue,
      });
    }

    filtered.push(data[data.length - 1]); // Keep last point
    return filtered;
  },
};
