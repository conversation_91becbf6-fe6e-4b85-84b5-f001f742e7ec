# ECG Monitoring Dashboard – Real-Time Streaming Architecture (Implementation Plan)

This plan replaces the current dummy/adhoc streaming with a robust, patient-specific real-time architecture across the ECG Server (FastAPI) and the Web dashboard (Next.js). It uses the proposed design in `streaming.md` and the carousel prototype in `carausol_ecg.txt` as foundations, and integrates with the existing Better Auth setup.

## 🎯 Key Changes: Production with Real Data

**IMPORTANT**: This implementation uses **real patient ECG recordings** from the database, **NOT simulated/synthetic data**.

### What's Different from Original Plan:

1. **✅ Real ECG Data** - Load actual patient recordings from `ecg_recordings` table (not `ECGSimulator`)
2. **✅ ML-Based Classification** - Use trained models for arrhythmia detection (not hard-coded conditions)
3. **✅ Database Storage** - Compressed ECG signals stored in PostgreSQL with metadata
4. **❌ No Simulation** - `ECGSimulator` is only a fallback for development when no recordings exist
5. **❌ No Hard-Coding** - Conditions come from ML predictions, not MRN-based mapping

### Implementation Status:

- ✅ **M1 Completed**: `PatientStreamRegistry` with `ECGDataLoader` integration
- 🔄 **M0 Pending**: Database schema for ECG recordings
- 🔄 **M3 Pending**: ML model integration for classification

---

## 1) Architecture Analysis

### 1.1 Current (observed)

- Backend (apps/ecg-server):
  - FastAPI WebSocket at `/ws/ecg/{patient_id}` (see `apps/ecg-server/src/main.py`).
  - Uses `ECGWebSocketManager` (see `apps/ecg-server/src/ecg/websocket_handler.py`) that streams samples on-demand per connection via `ECGSimulator.stream_patient_ecg(patient_id)`.
  - **⚠️ Uses synthetic/simulated ECG data** - Not real patient recordings.
  - **⚠️ Hard-coded conditions** - MRN-based mapping (MRN001=normal, MRN002=afib, etc.).
  - Validates session tokens via `SessionValidator` against `AUTH_SERVER_URL`. Authorization is not fully enforced (patient access checks need to be added).
  - Messages are JSON for each sample (`{"type":"signal","data":{"value":...}}`).
- Frontend (apps/web):
  - WebSocket client hook in `src/hooks/useECGStream.ts` connects with `ws://.../ws/ecg/${patientId}?token=...`.
  - Known issues (see `ECG_ISSUE_3_PERSISTENCE.md` and `ECG_ISSUES_SUMMARY.md`):
    - Patient switch does not fully reset the chart signal buffer.
    - WebSocket cleanup/reconnect edge cases.
    - Risk of mixing data across patients.
  - `.env` points to ECG server and default test patient.
- Display: Basic Canvas/Chart rendering exists; a carousel sketch (`carausol_ecg.txt`) shows a rolling canvas idea, but it’s a mock data stub.

### 1.2 Proposed (Production with Real Data)

- Backend (server):
  - **✅ Use real ECG recordings** from database (stored in `ecg_recordings` table).
  - **✅ Load patient-specific recordings** via `ECGDataLoader` (replaces `ECGSimulator`).
  - **✅ ML-based classification** - Real-time arrhythmia detection using trained models (not hard-coded conditions).
  - Maintain a continuous stream for each patient (producer tasks per patient) from patient-specific ECG sources (files or database), paced at real-time (360 Hz, chunked e.g. 0.25s → 90 samples). Producers run regardless of client connections.
  - WebSocket connections subscribe to the specific patient’s stream and receive only that patient’s chunks.
  - Start with in-process broadcaster/queues per patient; optionally swap to Redis Pub/Sub or Streams to scale horizontally (see `streaming.md`).
  - Prefer binary frames (Float32) for efficiency; optionally send an initial JSON header with fs/chunk_size; support JSON fallback behind a feature flag.
  - Enforce authentication AND authorization: only authorized users may subscribe to given patient.
- Frontend (web):
  - On dashboard load: fetch actual patient list from the database (tRPC in `apps/server`) and select the first patient by default.
  - For the selected patient: open WebSocket to patient-specific endpoint; reset chart buffers on switch; render rolling 10s window.
  - Decode binary Float32 frames (or JSON fallback), push to ring buffer, render efficiently (Canvas, or LightningChart if available).

### 1.3 Components to remove/modify/add

- Remove/Replace:
  - **❌ Remove `ECGSimulator`** - Replace with `ECGDataLoader` for real patient recordings.
  - **❌ Remove hard-coded conditions** - Replace with ML model predictions.
  - Replace any dummy random client-side generators (as in `carausol_ecg.txt`) with real WebSocket data.
- Modify:
  - `ECGWebSocketManager` to subscribe to per-patient producers instead of on-demand generation.
  - `useECGStream.ts` to harden patient switching, cleanup, reconnection, binary decoding, and buffer resets.
  - Chart component to handle ring buffer updates and patient switch resets.
- Add:
  - **✅ Database schema** for `ecg_recordings` and `ecg_classifications` tables.
  - **✅ ECGDataLoader** - Loads real ECG recordings from database.
  - **✅ ML Classifier** - Real-time arrhythmia detection (replaces hard-coded logic).
  - PatientStreamRegistry (server) – manages per-patient producers and broadcast channels.
  - Authorization checks (server) – verify user can access requested patient_id via DB.
  - Optional short-lived WS token minting route (apps/server) if we move away from passing raw session token.

---

## 2) Implementation Steps (Backend → Frontend)

Each step is independently testable with clear success criteria.

### Backend 0 — Database Schema for Real ECG Data (NEW - Priority 1)

**Status**: ✅ **COMPLETED** - Schema designed in `docs/real-ecg-data-architecture.md`

1. Add ECG recording tables to `apps/server/src/db/schema/ecg.ts`:

   - `ecg_recordings` table - Stores compressed ECG signal data with metadata
   - `ecg_classifications` table - Stores ML model predictions for ECG segments
   - Migration scripts to create tables in Neon database
   - Seed script to load sample ECG data (MIT-BIH or PhysioNet datasets)

2. Success criteria:
   - Tables created in database
   - Sample recordings loaded (at least 4-5 patients with different conditions)
   - Query `SELECT COUNT(*) FROM ecg_recordings` returns > 0

### Backend A — Real ECG Data Loader (UPDATED - Uses Real Data)

**Status**: ✅ **COMPLETED** - `ECGDataLoader` implemented in `apps/ecg-server/src/ecg/data_loader.py`

1. Implement `ECGDataLoader` to load real patient recordings:

   - Input: list of patients from Neon (via `DatabaseManager`).
   - Output: in-memory dict `{patient_id -> ECGRecording}` with real signal data from database.
   - Fetch latest recording for each patient from `ecg_recordings` table.
   - Decompress signal data (gzip compressed Float32 arrays).
   - Handle resampling if recording sample rate differs from target (360 Hz).
   - Fallback to synthetic data only if no recordings exist (development mode).

2. Success criteria:
   - `/health` includes `database_healthy=true`
   - Logs show "Loaded recording for patient X" (not "Generated synthetic ECG")
   - Recording count equals patient count
   - No hard-coded condition mappings used

### Backend B — Real-time producers per patient (UPDATED - Uses Real Data)

**Status**: ✅ **COMPLETED** - `PatientStreamRegistry` implemented in `apps/ecg-server/src/ecg/streams.py`

1. Implement `PatientStreamRegistry` with real data integration:

   - Modified to use `ECGDataLoader` instead of `ECGSimulator`
   - For each patient, spawn an asyncio Task that:
     - Loads real ECG recording from database via `ECGDataLoader.load_patient_recording()`
     - Loads/holds the patient’s ECG signal (file/memory) and maintains a read cursor.
     - Every `T_chunk=0.25s` sends `chunk_size=90` samples to a Broadcast channel or asyncio.Queue for that patient.
     - Wrap cursor on end; handle backoff if no consumers.
   - Provide APIs: `subscribe(patient_id) -> AsyncIterator[bytes|list]`, `start_all(patients)`, `stop_all()`.

2. Success criteria:
   - Test consumer receives real ECG data (not synthetic)
   - Chunks are from actual patient recordings
   - Timing is precise (360 Hz, 0.25s chunks = 90 samples)
   - Multiple subscribers receive identical real data

### Backend B.5 — Dataset Annotations Integration (NEW - Replaces Hard-Coded Conditions)

**Status**: 🔄 **PENDING** - To be implemented

**Approach**: Use pre-labeled ground truth from ECG datasets (MIT-BIH, PhysioNet) instead of ML model inference.

1. Load dataset annotations during data seeding:

   - Parse MIT-BIH annotation files (.atr) using WFDB library
   - Extract beat-level annotations (N, V, A, etc.) with timestamps
   - Create segment-level classifications (10-second windows)
   - Store in `ecg_annotations` and `ecg_classifications` tables
   - Confidence = 1.0 for all ground truth labels

2. Implementation:

   - Create `apps/ecg-server/scripts/load_mitbih_annotations.py` script
   - Add database schema for `ecg_annotations` and `ecg_classifications`
   - Update `ECGDataLoader` to load pre-computed classifications
   - Update `PatientProducer` to broadcast classifications at appropriate times
   - No ML model needed - use expert-annotated labels

3. Success criteria:
   - No hard-coded `if mrn == "MRN001": condition = "normal"` logic
   - Classifications loaded from database (ground truth annotations)
   - Beat-level and segment-level annotations stored
   - Classifications streamed to frontend via WebSocket
   - Confidence = 1.0 for all ground truth labels

### Backend C — WebSocket subscription-only handler

3. Adapt `ECGWebSocketManager`:
   - On connection: validate session token (existing code), then `authorize(user, patient_id)` using `DatabaseManager` (see Backend D).
   - Subscribe to `PatientStreamRegistry.subscribe(patient_id)` and forward chunks to client.
   - Use binary frames by default: send one JSON header on connect `{fs:360, chunk_size:90, dtype:'float32'}` then binary `Float32Array` chunks.
   - Handle backpressure: if `websocket.send_bytes` is slow, consider dropping older chunks or pausing consumer via queue size limits.
   - Success criteria: multiple clients connected to the same patient receive identical real-time chunks; CPU/network stable.

### Backend D — Authorization guard

4. Implement `authorize(user, patient_id)` in ECG server:
   - For doctor users: allowed if doctor is assigned to patient OR belongs to same organization with permission (define minimal policy, e.g., assignedDoctorId match or org membership).
   - For patient users: allowed if `user.id == patient.userId`.
   - For admins: allow per org scope as needed.
   - Use existing Neon DB via `DatabaseManager` to join `user`, `doctors`, `patients`, `organizations` as needed.
   - Return HTTP 403/WS close 1008 when unauthorized.
   - Success criteria: authorized user connects; unauthorized user is rejected and logged.

### Backend E — Health & observability

5. Augment `/health` with streaming stats: number of patients, active producers, consumers per patient, average chunk delay.
   - Success criteria: `/health` returns these metrics; basic sanity dashboards/logs.

### Frontend A — Fetch patients & default selection

6. On dashboard mount (apps/web):
   - Use tRPC from `apps/server` to fetch patient list from Neon.
   - Set `selectedPatientId = patients[0]?.id || null`.
   - Show loading/empty states appropriately.
   - Success criteria: Page loads with real patient list; first patient auto-selected.

### Frontend B — Harden WebSocket client (useECGStream)

7. Update `useECGStream.ts`:
   - Validate session with Better Auth before connecting (already present); keep using `?token=` query param for now.
   - On `patientId` change: close previous WS, clear reconnect timers, reset signal buffers, then open new WS.
   - Binary support: if header `{dtype:'float32'}` seen, set `ws.binaryType='arraybuffer'` and decode `Float32Array` chunks; otherwise JSON fallback.
   - Keep a ring buffer (e.g., last 10s) and a max classification list length.
   - Connection state flags, exponential backoff, and robust `onclose` cleanup.
   - Success criteria: Switching patients never mixes signals; connection stable and self-recovers.

### Frontend C — Chart/display component

8. Replace dummy carousel logic (`carausol_ecg.txt`) with production component:
   - Canvas or LightningChart-based renderer with a rolling window (e.g., 10s at 360Hz).
   - Efficient draw loop (requestAnimationFrame) and downsampling when necessary.
   - Visual markers for classifications when received.
   - Success criteria: Smooth rendering at 60 FPS on typical devices; accurate scaling; no memory growth across switches.

### Frontend D — Patient selection & routing

9. Implement patient selection UI (list or dropdown). On change, pass new `patientId` to `useECGStream` and chart.
   - Optional: Deep link (`/dashboard/ecg?patientId=...`).
   - Success criteria: Selecting different patients updates the stream immediately and cleanly.

### Frontend E — End-to-end testing page

10. Provide `/test-ecg` page (already present) with test harness:
    - Buttons to connect/disconnect, change patients, simulate slow client.
    - Show connection state, bytes/sec, chunk rate, classification count.
    - Success criteria: All states observable; manual QA straightforward.

---

## 3) Authentication & Authorization

### 3.1 WebSocket authentication options

- Current: Pass Better Auth session token via `?token=` query parameter and validate server-side in ECG server (`SessionValidator`). This is acceptable with TLS (wss) and short timeouts.
- Recommended hardening (phased):
  1. Keep current flow but enforce a short max lifetime for tokens during WS handshake.
  2. Add an apps/server endpoint to mint a short-lived WS token (JWT) scoped to `user_id` and optional `patient_id` claim; ECG server verifies signature and claims.

### 3.2 Authorization checks (server-side)

- Implement `authorize(user, patient_id)` at the ECG server using Neon DB:
  - Patient can access own data.
  - Doctor can access assigned patient (and/or organization scoped patients).
  - Admin can access org patients (configurable by role).
- Deny by default; close WS with code `1008` and reason `"Unauthorized"`.

### 3.3 Transport and CSRF

- Always use `wss://` in production; restrict allowed origins.
- Because custom headers aren’t supported in browser WebSocket API, prefer query token or same-site cookies if same-origin.
- Consider rotating/short-lived tokens to reduce leakage risk in URLs.

### 3.4 Auditing & rate limiting

- Log: who connected, when, which patient.
- Rate-limit connections per user/IP to protect server.

---

## 4) Replacement Mapping (current → proposed)

| Area            | Current                                            | Proposed (Production with Real Data)                           |
| --------------- | -------------------------------------------------- | -------------------------------------------------------------- |
| **Data Source** | **❌ ECGSimulator (synthetic data)**               | **✅ ECGDataLoader (real patient recordings from database)**   |
| **Conditions**  | **❌ Hard-coded (MRN-based mapping)**              | **✅ ML model predictions with confidence scores**             |
| **Storage**     | **❌ No persistent ECG data**                      | **✅ ecg_recordings table with compressed signal data**        |
| Producer        | On-demand `ECGSimulator.stream_patient_ecg` per WS | Continuous per-patient producers via `PatientStreamRegistry`   |
| Transport       | JSON per sample                                    | Binary Float32 chunks (with JSON header)                       |
| WS Auth         | Query `?token=session_cookie`                      | Short-lived WS token (phase 2), still validate via Better Auth |
| Authorization   | Not enforced                                       | Enforced via DB checks (doctor/patient/org)                    |
| Frontend init   | Sometimes uses test patient from env               | Fetch real patients, auto-select first                         |
| Patient switch  | Risk of mixing buffers                             | Cleanup + buffer reset + reconnect                             |
| Display         | Basic Canvas/Chart                                 | Optimized rolling window plot + classification markers         |

---

## 5) Milestones & Test Plan

### M0 – Database Schema & Real Data (NEW - Priority 1)

**Status**: 🔄 **PENDING**

- Add `ecg_recordings` and `ecg_classifications` tables to database schema
- Create migration scripts
- Load sample ECG data from MIT-BIH or PhysioNet datasets
- Verify: `SELECT COUNT(*) FROM ecg_recordings` returns > 0

### M1 – Backend producers + subscribe API (UPDATED - Uses Real Data)

**Status**: ✅ **COMPLETED**

- `PatientStreamRegistry` implemented with `ECGDataLoader`
- Unit-test producer timing and chunk size
- Subscribing without WS yields real ECG data stream
- Verify: Logs show "Loaded recording for patient X" (not "Generated synthetic ECG")

### M2 – WS subscription & binary frames

**Status**: 🔄 **PENDING**

- Connect with dev client; verify header then Float32Array decoding
- Two clients see same chunk cadence
- Verify: Real patient ECG data streamed (not synthetic)

### M3 – Dataset Annotations Integration (NEW)

**Status**: 🔄 **PENDING**

- Load MIT-BIH recordings with expert annotations
- Parse beat-level and segment-level classifications
- Store annotations in database during seeding
- Stream pre-computed classifications to frontend
- Verify: No hard-coded condition logic; all classifications from dataset ground truth

### M4 – AuthZ enforcement

**Status**: 🔄 **PENDING**

- Authorized connects; unauthorized gets 1008

### M5 – Frontend patient list + default selection

**Status**: 🔄 **PENDING**

- Dashboard loads real patients; first is selected

### M6 – Frontend WS client hardening

**Status**: 🔄 **PENDING**

- Switching patients resets buffer; no mixed data; reconnection stable

### M7 – Visualization & UX

**Status**: 🔄 **PENDING**

- 10s rolling window at 60 FPS; classification overlays

---

## 6) Implementation Notes (from streaming.md)

- Pace in real-time: `fs=360`, `T_chunk=0.25s`, `chunk_size=90`.
- Prefer binary frames (Float32/Int16) for efficiency; send initial header JSON once.
- Consider Redis Pub/Sub if running multiple workers or scaling out WS servers.

---

## 7) Acceptance Criteria

- Opening dashboard fetches real patients, auto-selects first one.
- Connecting to WS streams only that patient’s data.
- Unauthorized users cannot access non-permitted patients.
- Switching patients never mixes signals; buffers reset correctly.
- Stable, low-latency streaming with clear observability.
