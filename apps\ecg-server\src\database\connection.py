"""
Database connection and patient access verification.

This module provides database connectivity for verifying patient-doctor
relationships and managing ECG session data.
"""

import os
import logging
from typing import Optional, Dict, Any, List
from contextlib import asynccontextmanager

import asyncpg
from asyncpg import Connection, Pool
from .neon_connection import NeonHTTPConnection

logger = logging.getLogger(__name__)


class DatabaseManager:
    """
    Manages database connections and patient access verification.
    
    This class handles PostgreSQL connections using asyncpg and provides
    methods for verifying patient-doctor relationships for ECG access control.
    """
    
    def __init__(self, database_url: Optional[str] = None) -> None:
        """
        Initialize the database manager.
        
        Args:
            database_url: PostgreSQL connection URL. If None, uses DATABASE_URL env var.
        """
        self.database_url = database_url or os.getenv("DATABASE_URL")
        self.pool: Optional[Pool] = None
        self.http_connection: Optional[NeonHTTPConnection] = None
        self.use_http_fallback = False
        
        if not self.database_url:
            raise ValueError("DATABASE_URL environment variable is required")
        
        logger.info("🗄️  DatabaseManager initialized")
    
    async def create_pool(self, min_size: int = 5, max_size: int = 20) -> None:
        """
        Create connection pool for database operations.
        
        Args:
            min_size: Minimum number of connections in pool
            max_size: Maximum number of connections in pool
        """
        try:
            # Try asyncpg connection first
            self.pool = await asyncpg.create_pool(
                self.database_url,
                min_size=min_size,
                max_size=max_size,
                command_timeout=30
            )
            logger.info(f"✅ Database connection pool created (min: {min_size}, max: {max_size})")
            self.use_http_fallback = False
            
        except Exception as e:
            logger.warning(f"⚠️  Failed to create asyncpg pool: {e}")
            logger.info("🔄 Attempting HTTP-based connection as fallback...")
            
            try:
                # Fall back to HTTP-based connection
                self.http_connection = NeonHTTPConnection(self.database_url)
                await self.http_connection.health_check()
                self.use_http_fallback = True
                logger.info("✅ HTTP-based database connection established")
                
            except Exception as http_error:
                logger.error(f"❌ HTTP fallback also failed: {http_error}")
                raise Exception(f"Both asyncpg and HTTP connections failed. asyncpg: {e}, HTTP: {http_error}")
    
    async def close_pool(self) -> None:
        """Close the database connection pool."""
        if self.pool:
            await self.pool.close()
            logger.info("🔒 Database connection pool closed")
    
    async def _execute_query(self, query: str, *params):
        """
        Execute a database query using either asyncpg or HTTP fallback.
        
        Args:
            query: SQL query to execute
            params: Query parameters
            
        Returns:
            Query result
        """
        if self.use_http_fallback and self.http_connection:
            return await self.http_connection.fetchval(query, *params)
        
        # Use asyncpg connection
        if not self.pool:
            await self.create_pool()
        
        async with self.pool.acquire() as connection:
            return await connection.fetchval(query, *params)
    
    async def _execute_fetch(self, query: str, *params):
        """
        Execute a query and fetch all rows.
        
        Args:
            query: SQL query to execute
            params: Query parameters
            
        Returns:
            List of rows
        """
        if self.use_http_fallback and self.http_connection:
            return await self.http_connection.fetch(query, *params)
        
        # Use asyncpg connection
        if not self.pool:
            await self.create_pool()
        
        async with self.pool.acquire() as connection:
            return await connection.fetch(query, *params)
    
    async def _execute_fetchrow(self, query: str, *params):
        """
        Execute a query and fetch first row.
        
        Args:
            query: SQL query to execute
            params: Query parameters
            
        Returns:
            First row or None
        """
        if self.use_http_fallback and self.http_connection:
            return await self.http_connection.fetchrow(query, *params)
        
        # Use asyncpg connection
        if not self.pool:
            await self.create_pool()
        
        async with self.pool.acquire() as connection:
            return await connection.fetchrow(query, *params)

    async def verify_patient_access(self, doctor_user_id: str, patient_id: str) -> bool:
        """
        Verify that a doctor has access to a patient's ECG data.
        
        Args:
            doctor_user_id: The doctor's user ID from authentication
            patient_id: The patient ID to verify access for
            
        Returns:
            True if doctor has access to patient, False otherwise
        """
        try:
            # Query to check if doctor is assigned to this patient
            query = """
                SELECT COUNT(*) as count
                FROM patients p
                JOIN doctors d ON p.assigned_doctor_id = d.id
                WHERE p.id = $1 AND d.user_id = $2
            """

            result = await self._execute_query(query, patient_id, doctor_user_id)
            has_access = result > 0

            if has_access:
                logger.debug(f"✅ Doctor {doctor_user_id} has access to patient {patient_id}")
            else:
                logger.warning(f"🚫 Doctor {doctor_user_id} denied access to patient {patient_id}")

                # In development mode, allow access to test patients
                import os
                if os.getenv("DEBUG", "false").lower() == "true":
                    test_patient_ids = ["test-patient-1", "QqGq_lQX8-IMQqSjE_fPV"]
                    if patient_id in test_patient_ids:
                        logger.info(f"🔧 Development mode: Granting access to test patient {patient_id}")
                        return True

            return has_access

        except Exception as e:
            logger.error(f"❌ Error verifying patient access: {e}")
            # In development mode, allow access as fallback
            import os
            if os.getenv("DEBUG", "false").lower() == "true":
                logger.info(f"🔧 Development mode: Granting fallback access due to error")
                return True
            return False
    
    async def get_patient_info(self, patient_id: str) -> Optional[Dict[str, Any]]:
        """
        Get patient information by patient ID.
        
        Args:
            patient_id: The patient ID to retrieve
            
        Returns:
            Dictionary with patient information or None if not found
        """
        try:
            query = """
                SELECT 
                    p.id,
                    p.medical_record_number,
                    u.name,
                    u.email,
                    p.date_of_birth,
                    p.gender,
                    p.emergency_contact,
                    p.emergency_phone,
                    p.assigned_doctor_id,
                    d.user_id as doctor_user_id,
                    du.name as doctor_name
                FROM patients p
                JOIN "user" u ON p.user_id = u.id
                LEFT JOIN doctors d ON p.assigned_doctor_id = d.id
                LEFT JOIN "user" du ON d.user_id = du.id
                WHERE p.id = $1
            """
            
            row = await self._execute_fetchrow(query, patient_id)
            
            if row:
                return dict(row)
            else:
                logger.warning(f"🚫 Patient not found: {patient_id}")
                return None
                    
        except Exception as e:
            logger.error(f"❌ Error getting patient info: {e}")
            return None
    
    async def get_doctor_patients(self, doctor_user_id: str) -> List[Dict[str, Any]]:
        """
        Get all patients assigned to a doctor.
        
        Args:
            doctor_user_id: The doctor's user ID
            
        Returns:
            List of patient dictionaries
        """
        try:
            query = """
                SELECT 
                    p.id,
                    p.medical_record_number,
                    u.name,
                    u.email,
                    p.date_of_birth,
                    p.gender,
                    p.created_at
                FROM patients p
                JOIN "user" u ON p.user_id = u.id
                JOIN doctors d ON p.assigned_doctor_id = d.id
                WHERE d.user_id = $1
                ORDER BY u.name
            """
            
            rows = await self._execute_fetch(query, doctor_user_id)
            return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"❌ Error getting doctor's patients: {e}")
            return []
    
    async def create_ecg_session(
        self, 
        patient_id: str, 
        doctor_id: str, 
        sample_rate: int = 360,
        notes: Optional[str] = None
    ) -> Optional[str]:
        """
        Create a new ECG monitoring session.
        
        Args:
            patient_id: The patient ID
            doctor_id: The doctor ID (not user_id, but actual doctor record ID)
            sample_rate: ECG sample rate in Hz
            notes: Optional session notes
            
        Returns:
            Session ID if created successfully, None otherwise
        """
        try:
            # Generate a unique session ID
            import uuid
            session_id = str(uuid.uuid4())
            
            query = """
                INSERT INTO ecg_sessions (
                    id, patient_id, doctor_id, sample_rate, status, notes
                ) VALUES ($1, $2, $3, $4, 'active', $5)
                RETURNING id
            """
            
            result = await self._execute_query(
                query, session_id, patient_id, doctor_id, sample_rate, notes
            )
            
            logger.info(f"✅ Created ECG session: {session_id}")
            return result or session_id
                
        except Exception as e:
            logger.error(f"❌ Error creating ECG session: {e}")
            return None
    
    async def end_ecg_session(self, session_id: str) -> bool:
        """
        End an active ECG session.
        
        Args:
            session_id: The session ID to end
            
        Returns:
            True if session ended successfully, False otherwise
        """
        try:
            query = """
                UPDATE ecg_sessions 
                SET 
                    status = 'completed',
                    end_time = NOW(),
                    duration = EXTRACT(EPOCH FROM (NOW() - start_time))::INTEGER,
                    updated_at = NOW()
                WHERE id = $1 AND status = 'active'
                RETURNING id
            """
            
            result = await self._execute_query(query, session_id)
            
            if result:
                logger.info(f"✅ Ended ECG session: {session_id}")
                return True
            else:
                logger.warning(f"🚫 Session not found or already ended: {session_id}")
                return False
                    
        except Exception as e:
            logger.error(f"❌ Error ending ECG session: {e}")
            return False
    
    async def health_check(self) -> bool:
        """
        Check database connectivity.
        
        Returns:
            True if database is accessible, False otherwise
        """
        try:
            if self.use_http_fallback and self.http_connection:
                return await self.http_connection.health_check()
            
            result = await self._execute_query("SELECT 1")
            return result == 1
        except Exception as e:
            logger.error(f"❌ Database health check failed: {e}")
            return False