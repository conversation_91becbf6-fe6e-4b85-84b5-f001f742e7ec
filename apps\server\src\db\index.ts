import { config } from "dotenv";
import { neon, neonConfig } from "@neondatabase/serverless";
import { drizzle } from "drizzle-orm/neon-http";
import ws from "ws";

// Load environment variables first
config();

// Import all schema tables
import * as authSchema from "./schema/auth";
import * as organizationsSchema from "./schema/organizations";
import * as doctorsSchema from "./schema/doctors";
import * as ecgSchema from "./schema/ecg";

neonConfig.webSocketConstructor = ws;

// To work in edge environments (Cloudflare Workers, Vercel Edge, etc.), enable querying over fetch
// neonConfig.poolQueryViaFetch = true

const sql = neon(process.env.DATABASE_URL || "");
export const db = drizzle(sql, {
	schema: {
		...authSchema,
		...organizationsSchema,
		...doctorsSchema,
		...ecgSchema,
	},
});

// Re-export all schema tables for easy access
export * from "./schema/auth";
export * from "./schema/organizations";
export * from "./schema/doctors";
export * from "./schema/ecg";
