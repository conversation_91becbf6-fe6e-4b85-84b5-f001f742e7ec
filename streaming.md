Nice — I’ll give you a practical, production-minded design plus copy-pasteable code examples so you can implement it end-to-end. Goal: **(A)** deterministically assign a random MIT-BIH record to each patient (using a seed), and **(B)** stream ECG samples in (near) real-time to frontend clients efficiently and safely.

I’ll show:

1. deterministic assignment (seeded)
2. a simple streaming worker that *paces* data to real time
3. a scalable architecture (single server → Redis pub/sub → multi-worker)
4. data format, chunking, backpressure, and security recommendations
5. a minimal FastAPI WebSocket + frontend example

---

# 1) Deterministic random assignment (with seed)

Two sensible approaches:

* **Shuffle & zip** — ensures a one-to-one mapping if you have at least as many records as patients. Use `random.Random(seed).shuffle(records)` so mapping is reproducible.
* **Hashing** — if you must allow reuse or want O(1) mapping: `index = hash(patient_id + seed) % len(records)`.

Example (Python):

```python
import random

def assign_records_to_patients(patient_ids, record_ids, seed=42, allow_reuse=False):
    rng = random.Random(seed)
    if allow_reuse:
        # deterministic per patient using hashing (allows reuse)
        return {pid: record_ids[hash(str(pid) + str(seed)) % len(record_ids)]
                for pid in patient_ids}
    else:
        # make reproducible one-to-one mapping (requires len(record_ids) >= len(patient_ids))
        records = list(record_ids)
        rng.shuffle(records)
        mapping = {}
        for i, pid in enumerate(patient_ids):
            mapping[pid] = records[i % len(records)]  # if more patients than records, this will reuse
        return mapping
```

Store the final mapping in your DB as `assigned_record_id` for auditability and to survive restarts.

---

# 2) Streaming design basics (real-time simulation)

ECG data are arrays sampled at `fs` (usually 360 Hz for MIT-BIH). You must stream samples paced by real time:

* Choose a **chunk duration** `T_chunk` (e.g., 0.25s or 1s).
* `chunk_size = int(fs * T_chunk)` samples per message.
* Worker sends chunk every `T_chunk` seconds using `await asyncio.sleep(T_chunk)` so clients receive real-time pacing.
* For efficiency, send binary frames (int16/float32) rather than JSON arrays (less bandwidth & serialization cost). If you must use JSON, send compressed/base64.

Recommended defaults:

* `fs = 360` → `T_chunk = 0.25`s → `chunk_size = 90` samples. This gives ~250ms latency and enough samples for smooth plotting.

---

# 3) Simple single-server implementation (FastAPI + WebSocket)

This example streams prerecorded ECG arrays (loaded in memory or from disk) in paced chunks. It’s simple and good for prototypes.

```python
# server.py
import asyncio
import numpy as np
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Depends, HTTPException
import wfdb   # if reading MIT-BIH files; optional
import uvicorn
from typing import Dict
import json

app = FastAPI()

# Example: load precomputed ECG signals into memory (dict: record_id -> np.ndarray)
# Each array shape: (n_samples, n_leads) or (n_samples,) single-lead
ECG_STORE: Dict[str, np.ndarray] = {}   # populate at startup

FS = 360
T_CHUNK = 0.25
CHUNK_SIZE = int(FS * T_CHUNK)

# Simple auth stub (replace with JWT or session)
async def get_current_patient(websocket: WebSocket):
    # Example: expect query param ?patient_id=123
    pid = websocket.query_params.get("patient_id")
    if not pid:
        raise HTTPException(401, "missing patient_id")
    return pid

@app.on_event("startup")
async def load_ecg_data():
    # Example load: you can instead load from DB or disk.
    # For demo, create a synthetic signal
    import math
    for rec in ["100","101","102"]:
        t = np.arange(0, 60*30*FS) / FS   # 30 minutes signal length
        ecg = (np.sin(2*math.pi*1.0*t) + 0.1*np.random.randn(len(t))).astype(np.float32)
        ECG_STORE[rec] = ecg

@app.websocket("/ws/stream")
async def ws_stream(websocket: WebSocket, patient_id: str = Depends(get_current_patient)):
    await websocket.accept()
    # lookup assigned record for this patient from DB (here we'll hardcode for demo)
    # e.g., assigned_record = query_db_for_assigned_record(patient_id)
    assigned_record = "100"   # replace with DB lookup

    if assigned_record not in ECG_STORE:
        await websocket.close(code=1008)
        return

    signal = ECG_STORE[assigned_record]
    n = len(signal)
    pos = 0  # starting position (persist this in DB if wanted)

    try:
        while True:
            end = min(pos + CHUNK_SIZE, n)
            chunk = signal[pos:end]

            # send as binary: float32 little-endian
            await websocket.send_bytes(chunk.tobytes())

            # advance and wrap or stop
            pos = end if end < n else 0

            # pace to real time
            await asyncio.sleep(T_CHUNK)
    except WebSocketDisconnect:
        print("client disconnected")
    except Exception as e:
        print("error", e)
        await websocket.close()
```

Notes:

* This sends **raw float32 bytes**. Client must decode as Float32Array.
* Persist `pos` per patient if you want reconnect/resume.
* Replace the hardcoded assigned_record with DB lookup.

---

# 4) Frontend minimal client (WebSocket binary)

Example JS to connect and convert bytes into a waveform array for plotting:

```javascript
const ws = new WebSocket("wss://yourserver.example/ws/stream?patient_id=123");

ws.binaryType = "arraybuffer";

ws.onopen = () => console.log("connected");
ws.onmessage = (ev) => {
  const buffer = ev.data; // ArrayBuffer
  // interpret as float32 (little-endian)
  const samples = new Float32Array(buffer);
  // append to plot buffer, render
  console.log("received", samples.length);
};
ws.onclose = () => console.log("closed");
```

For plotting, use a canvas with a rolling buffer (e.g., keep last N seconds) or use specialized libs (Plotly, Chart.js).

---

# 5) Scaling & robustness (production patterns)

### Use Redis / message broker when multiple servers/workers exist

* **Producer/worker**: a process that reads ECG files and *publishes* chunk messages to `channel:patient:{patient_id}` (or Redis Stream).
* **WebSocket servers**: subscribe to Redis channel(s) for the patient(s) requested and forward incoming messages to connected WebSocket clients.
* Advantages: you can run multiple streamers and multiple WebSocket servers; the mapping persists; you decouple generation from client sockets.

Example architecture:

```
ECG Worker(s) --publish--> Redis Pub/Sub channel patient:123
WebSocket server(s) --subscribe--> Redis --> forward to websocket clients
Frontend clients <--WebSocket-- WebSocket server
```

Use **Redis Streams** (or Kafka) if you want persistence / replay / consumer groups.

### Handle slow clients & backpressure

* Limit messages per second (chunking as above).
* Use binary frames and keep small chunks (100–500ms).
* Track client send queue size and drop or reduce quality for slow clients.
* Implement ping/pong and disconnect idle clients.

### Authentication & Authorization

* Use JWT tokens or session cookies. Validate that a client is allowed to stream a particular patient's data.
* Use TLS (wss://).
* Audit logs for who requested which patient stream.

### Monitoring & observability

* Track metrics: connections, bytes/sec, message latencies.
* Add tracing to detect bottlenecks in Redis or network.

---

# 6) Data format recommendations

* **Binary**: send float32 or int16 samples as raw bytes. Small header optional:

  * Header (JSON once): `{"fs":360,"lead":"MLII","dtype":"float32","chunk_size":90}`
  * Then subsequent messages = raw sample bytes.
* **JSON** (if you must): `{"ts":169..., "samples":[...], "lead":"MLII"}` — bigger, slower, but easy to debug.
* **Protocol Buffers** or **MessagePack** are good middle grounds (compact, typed).

---

# 7) Additional features to consider

* **Multiple leads**: send interleaved arrays or separate channels (e.g., `patient:123:lead:MLII`)
* **Control messages**: client can request pause/resume, seek to timestamp, change chunk_size, or request start/stop.
* **Replay**: support playback speed control (x1, x2, x0.5) by changing sleep timing.
* **Storage of offsets**: store per patient the last stream index so clients can resume where they left off.

---

# 8) Example: Redis pub/sub producer + FastAPI subscriber snippet

Producer (worker) publishes:

```python
# producer.py
import asyncio, aioredis, numpy as np

REDIS_URL = "redis://localhost"

async def produce(patient_id, signal, fs=360):
    chunk_size = int(fs * 0.25)
    redis = await aioredis.create_redis_pool(REDIS_URL)
    pos = 0
    while True:
        chunk = signal[pos:pos+chunk_size].astype('float32').tobytes()
        # publish binary via base64 or use Redis Streams (binary-safe)
        await redis.publish(f"patient:{patient_id}", chunk)
        pos = (pos + chunk_size) % len(signal)
        await asyncio.sleep(0.25)

# run multiple producers in background for assigned patients
```

Subscriber (WebSocket server) subscribes and forwards to connected clients:

* In FastAPI WS endpoint, spawn a task that subscribes to Redis pubsub and `await pubsub.get_message()` then `await ws.send_bytes(...)`.

---

# 9) Summary — recommended default stack

* **Backend**: FastAPI + Uvicorn (async)
* **Streaming bus**: Redis Pub/Sub or Redis Streams (grow to Kafka if heavy)
* **Serialization**: send raw binary float32/int16 frames for efficiency
* **Chunking**: 0.25s chunks (90 samples at 360 Hz) — adjust to latency requirements
* **Auth**: plug in the currently use auth
* **Scaling**: decouple worker (producer) and websocket servers using Redis; run multiple instances behind LB.
* **Persistence**: store assigned_record_id and last_offset in DB for resume and auditing.

---


