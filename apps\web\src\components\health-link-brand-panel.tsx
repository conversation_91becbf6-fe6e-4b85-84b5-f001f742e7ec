const imgLogo = "/assets/logo.svg";

export default function HealthLinkBrandPanel() {
  return (
    <div className="h-full flex items-center justify-center">
      <div className="bg-[#e7542a] w-full max-w-[710px] h-[990px] rounded-[30px] flex flex-col items-center justify-between overflow-hidden">
        {/* Top curved white section */}
        <div className="w-full">
          <div className="flex justify-center">
            <div className="bg-white h-[54px] w-64 rounded-bl-[35px] rounded-br-[35px]" />
          </div>
        </div>
        
        {/* Main content */}
        <div className="flex-1 flex flex-col items-center justify-center px-[50px] py-[133px] gap-[200px]">
          {/* Logo and title section */}
          <div className="flex flex-col items-center justify-center gap-[45px] w-full">
            <div className="w-[268px] h-[264px]">
              <img 
                src={imgLogo} 
                alt="Health Link Logo" 
                className="w-full h-full object-contain"
              />
            </div>
            <h1 className="font-['Inria_Serif'] font-bold text-[64px] text-[#0c1421] leading-normal whitespace-nowrap">
              Health Link
            </h1>
          </div>
          
          {/* Description section */}
          <div className="flex flex-col items-center justify-start gap-5 w-full font-medium">
            <div className="text-center">
              <p className="font-['Inter'] font-medium text-[32px] text-[#332020] leading-normal">
                Manage Your Patients Seamlessly
              </p>
              <p className="font-['Inter'] font-medium text-[32px] text-[#332020] leading-normal">
                with Health link
              </p>
            </div>
            <div className="w-[525px]">
              <p className="font-['Inter'] font-medium text-[20px] text-[#332020] leading-normal text-center">
                Log in to manage, and moniter your patients with ease.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}