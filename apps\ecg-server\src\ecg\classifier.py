"""
ECG Classification Module for HealthLink

This module provides ECG signal classification for arrhythmia detection.
In production, this would use trained ML models for accurate classification.
"""

import numpy as np
import logging
from typing import Dict, List, Any
from scipy import signal as scipy_signal

logger = logging.getLogger(__name__)


class ECGClassifier:
    """
    ECG signal classifier for arrhythmia detection.
    
    This is a simplified implementation for demonstration.
    In production, this would use trained deep learning models.
    """
    
    def __init__(self):
        """Initialize the ECG classifier."""
        self.sample_rate = 360  # Hz
        self.classification_classes = [
            "normal",
            "atrial_fibrillation", 
            "ventricular_tachycardia",
            "bradycardia",
            "tachycardia",
            "premature_ventricular_contraction"
        ]
        
        logger.info("🧠 ECGClassifier initialized")
    
    async def classify_segment(self, signal_segment: List[float]) -> Dict[str, Any]:
        """
        Classify an ECG signal segment.
        
        Args:
            signal_segment: List of ECG sample values
            
        Returns:
            Classification result dictionary
        """
        try:
            # Convert to numpy array for processing
            signal_array = np.array(signal_segment)
            
            # Preprocess the signal
            processed_signal = self._preprocess_signal(signal_array)
            
            # Extract features
            features = self._extract_features(processed_signal)
            
            # Perform classification (simplified rule-based for demo)
            classification_result = self._classify_features(features)
            
            logger.debug(f"🔍 ECG classified as: {classification_result['class']} "
                        f"(confidence: {classification_result['confidence']:.2f})")
            
            return classification_result
            
        except Exception as e:
            logger.error(f"❌ ECG classification failed: {e}")
            return {
                "class": "unknown",
                "confidence": 0.0,
                "is_abnormal": False,
                "error": str(e)
            }
    
    def _preprocess_signal(self, signal_array: np.ndarray) -> np.ndarray:
        """
        Preprocess ECG signal for classification.
        
        Args:
            signal_array: Raw ECG signal
            
        Returns:
            Preprocessed ECG signal
        """
        # Remove baseline wander (high-pass filter)
        sos_hp = scipy_signal.butter(4, 0.5, btype='high', fs=self.sample_rate, output='sos')
        signal_hp = scipy_signal.sosfilt(sos_hp, signal_array)
        
        # Remove high-frequency noise (low-pass filter)
        sos_lp = scipy_signal.butter(4, 40, btype='low', fs=self.sample_rate, output='sos')
        signal_filtered = scipy_signal.sosfilt(sos_lp, signal_hp)
        
        # Normalize signal
        signal_normalized = (signal_filtered - np.mean(signal_filtered)) / np.std(signal_filtered)
        
        return signal_normalized
    
    def _extract_features(self, signal_array: np.ndarray) -> Dict[str, float]:
        """
        Extract features from ECG signal for classification.
        
        Args:
            signal_array: Preprocessed ECG signal
            
        Returns:
            Dictionary of extracted features
        """
        features = {}
        
        # Heart rate estimation
        features['heart_rate'] = self._estimate_heart_rate(signal_array)
        
        # QRS detection and analysis
        qrs_peaks = self._detect_qrs_peaks(signal_array)
        features['qrs_count'] = len(qrs_peaks)
        features['rr_intervals'] = self._calculate_rr_intervals(qrs_peaks)
        
        # Heart rate variability
        if len(features['rr_intervals']) > 1:
            features['hrv_std'] = np.std(features['rr_intervals'])
            features['hrv_mean'] = np.mean(features['rr_intervals'])
        else:
            features['hrv_std'] = 0.0
            features['hrv_mean'] = 0.0
        
        # Signal quality metrics
        features['signal_amplitude'] = np.max(signal_array) - np.min(signal_array)
        features['signal_noise'] = np.std(signal_array)
        
        return features
    
    def _estimate_heart_rate(self, signal_array: np.ndarray) -> float:
        """
        Estimate heart rate from ECG signal.
        
        Args:
            signal_array: ECG signal
            
        Returns:
            Estimated heart rate in BPM
        """
        # Simple peak detection for heart rate estimation
        peaks, _ = scipy_signal.find_peaks(signal_array, height=0.3, distance=self.sample_rate//3)
        
        if len(peaks) < 2:
            return 60.0  # Default heart rate
        
        # Calculate average RR interval
        rr_intervals = np.diff(peaks) / self.sample_rate  # Convert to seconds
        avg_rr = np.mean(rr_intervals)
        
        # Convert to BPM
        heart_rate = 60.0 / avg_rr if avg_rr > 0 else 60.0
        
        return float(heart_rate)
    
    def _detect_qrs_peaks(self, signal_array: np.ndarray) -> np.ndarray:
        """
        Detect QRS peaks in ECG signal.
        
        Args:
            signal_array: ECG signal
            
        Returns:
            Array of QRS peak indices
        """
        # Simple QRS detection using peak finding
        peaks, _ = scipy_signal.find_peaks(
            signal_array, 
            height=0.3,  # Minimum peak height
            distance=self.sample_rate//3  # Minimum distance between peaks (200ms)
        )
        
        return peaks
    
    def _calculate_rr_intervals(self, qrs_peaks: np.ndarray) -> List[float]:
        """
        Calculate RR intervals from QRS peaks.
        
        Args:
            qrs_peaks: Array of QRS peak indices
            
        Returns:
            List of RR intervals in seconds
        """
        if len(qrs_peaks) < 2:
            return []
        
        rr_intervals = np.diff(qrs_peaks) / self.sample_rate
        return rr_intervals.tolist()
    
    def _classify_features(self, features: Dict[str, float]) -> Dict[str, Any]:
        """
        Classify ECG based on extracted features.
        
        Args:
            features: Dictionary of ECG features
            
        Returns:
            Classification result
        """
        heart_rate = features.get('heart_rate', 60.0)
        hrv_std = features.get('hrv_std', 0.0)
        
        # Simple rule-based classification (for demonstration)
        if heart_rate < 50:
            classification = "bradycardia"
            confidence = 0.85
            is_abnormal = True
        elif heart_rate > 120:
            classification = "tachycardia"
            confidence = 0.80
            is_abnormal = True
        elif hrv_std > 0.2:  # High heart rate variability
            classification = "atrial_fibrillation"
            confidence = 0.70
            is_abnormal = True
        elif hrv_std < 0.02:  # Very low heart rate variability
            classification = "ventricular_tachycardia"
            confidence = 0.65
            is_abnormal = True
        else:
            classification = "normal"
            confidence = 0.90
            is_abnormal = False
        
        # Add some randomness to simulate real classification uncertainty
        confidence += np.random.normal(0, 0.05)
        confidence = np.clip(confidence, 0.1, 0.99)
        
        return {
            "class": classification,
            "confidence": float(confidence),
            "is_abnormal": is_abnormal,
            "heart_rate": heart_rate,
            "features": features
        }
