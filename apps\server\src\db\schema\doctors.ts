import { pgTable, text, timestamp, boolean, index, uniqueIndex } from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import { user } from "./auth";
import { organizations } from "./organizations";

export const doctors = pgTable("doctors", {
	id: text("id").primaryKey(),
	userId: text("user_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" }),
	organizationId: text("organization_id")
		.notNull()
		.references(() => organizations.id, { onDelete: "cascade" }),
	employeeId: text("employee_id"),
	department: text("department").notNull(),
	specialization: text("specialization").notNull(),
	phone: text("phone"),
	licenseNumber: text("license_number"),
	isVerified: boolean("is_verified").notNull().default(false),
	createdAt: timestamp("created_at").notNull().default(sql`now()`),
	updatedAt: timestamp("updated_at").notNull().default(sql`now()`),
}, (table) => ({
	doctorUserOrgUnique: uniqueIndex("uq_doctor_user_org").on(table.userId, table.organizationId),
	doctorOrgIdx: index("idx_doctor_org_id").on(table.organizationId),
	doctorUserIdx: index("idx_doctor_user_id").on(table.userId),
}));