# LightningChart JS License Configuration Guide

## 🔍 **License Types & Usage**

### **1. Trial Mode (Current Setup)**

- **Usage**: Development and testing
- **Features**: Full functionality with watermark
- **Configuration**: No license key required
- **Internet**: Not required
- **Limitations**: Watermark overlay on charts

```typescript
// Trial mode configuration (current)
export const LIGHTNINGCHART_CONFIG = {
  // No license key = trial mode
  licenseInformation: {
    appTitle: "HealthLink ECG Monitor",
    company: "HealthLink Medical Systems",
  },
};
```

### **2. Developer License**

- **Usage**: Individual developer environments
- **Features**: Full functionality, no watermark
- **Configuration**: Requires developer license key
- **Internet**: Required for validation
- **Limitations**: Single session per license

```typescript
// Developer license configuration
export const LIGHTNINGCHART_CONFIG = {
  license: "0002-your-developer-license-key-here",
  licenseInformation: {
    appTitle: "HealthLink ECG Monitor",
    company: "HealthLink Medical Systems",
  },
};
```

### **3. Web Deployment License**

- **Usage**: Production deployment to specific domains
- **Features**: Full functionality, no watermark
- **Configuration**: Requires deployment license key
- **Internet**: Not required
- **Limitations**: Domain-locked

```typescript
// Deployment license configuration
export const LIGHTNINGCHART_CONFIG = {
  license: "0002-your-deployment-license-key-here",
  licenseInformation: {
    appTitle: "HealthLink ECG Monitor",
    company: "HealthLink Medical Systems",
  },
};
```

## 🛠️ **Common License Issues & Solutions**

### **Issue: "License key validation failed: Deployment key is invalid"**

**Causes:**

1. Using developer license in deployment context
2. License key format mismatch
3. Domain not configured for deployment license

**Solutions:**

1. **For Development**: Use trial mode (remove license key)
2. **For Production**: Get proper deployment license for your domain
3. **Check License Type**: Ensure you're using the correct license type

### **Issue: "Session exists already"**

**Cause**: Developer license used in multiple sessions/browsers
**Solution**:

- Close other sessions
- Wait 1-2 minutes for session reset
- Use deployment license for multi-session scenarios

### **Issue: License version mismatch**

**Cause**: Using wrong license version (0001 vs 0002)
**Solution**: Use version 0002 for LightningChart v4.0.0+

## 🔧 **Environment-Based Configuration**

For different environments, you can use environment variables:

```typescript
// Environment-based configuration
export function getLightningChartConfig() {
  const isDevelopment = process.env.NODE_ENV === "development";

  if (isDevelopment) {
    // Trial mode for development
    return {
      licenseInformation: {
        appTitle: "HealthLink ECG Monitor (Dev)",
        company: "HealthLink Medical Systems",
      },
    };
  }

  // Production configuration
  return {
    license: process.env.NEXT_PUBLIC_LIGHTNINGCHART_LICENSE,
    licenseInformation: {
      appTitle: "HealthLink ECG Monitor",
      company: "HealthLink Medical Systems",
    },
  };
}
```

## 📋 **License Management Checklist**

### **For Development:**

- [ ] Use trial mode (no license key)
- [ ] Accept watermark overlay
- [ ] Test all chart functionality

### **For Production:**

- [ ] Purchase deployment license
- [ ] Configure domains in Customer Portal
- [ ] Generate deployment key
- [ ] Set environment variable
- [ ] Test on target domain

### **For Team Development:**

- [ ] Purchase developer licenses (1 per developer)
- [ ] Distribute keys through Customer Portal
- [ ] Set up team management
- [ ] Configure session management

## 🌐 **Customer Portal Access**

1. **Access Portal**: [LightningChart Customer Portal](https://portal.lightningchart.com)
2. **View Licenses**: Portal > Licenses
3. **Download Keys**: Click "Download license key" button
4. **Configure Domains**: Portal > Deployment Domains
5. **Team Management**: Portal > Team Management

## 📞 **Support & Resources**

- **Documentation**: [LightningChart JS Docs](https://lightningchart.com/js-charts/docs/)
- **License Guide**: [License Documentation](https://lightningchart.com/js-charts/docs/licenses/)
- **Support**: Contact LightningChart support for license issues
- **Examples**: [Interactive Examples](https://lightningchart.com/js-charts/interactive-examples/)

## 🚀 **Current Status**

✅ **Fallback System**: Canvas chart used when LightningChart license unavailable
✅ **ECG Functionality**: Real-time data streaming operational
✅ **Performance**: Optimized for 360Hz ECG data
⚠️ **LightningChart**: Requires license key (trial/developer/deployment)

## 🔑 **Getting a LightningChart License**

### **Option 1: Trial License (Recommended)**

1. Visit [LightningChart Non-Commercial License](https://lightningchart.com/non-commercial-license/)
2. Download trial license key
3. Add to configuration file
4. Enjoy full features with watermark

### **Option 2: Developer License**

1. Contact LightningChart sales
2. Purchase developer license
3. Access Customer Portal
4. Download license key

### **Option 3: Deployment License**

1. Purchase deployment license for your domain
2. Configure domains in Customer Portal
3. Generate deployment key
4. Use for production deployment

## 🔧 **Applying Your License**

Once you have a license key, update the configuration:

```typescript
// apps/web/src/lib/lightningchart-config.ts
export const LIGHTNINGCHART_CONFIG = {
  license: "0002-your-license-key-here",
  licenseInformation: {
    appTitle: "HealthLink ECG Monitor",
    company: "HealthLink Medical Systems",
  },
} as const;
```
